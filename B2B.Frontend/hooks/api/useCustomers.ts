'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api/client';
import {
    CustomerListDto,
    CustomerDto,
    CustomerCreateDto,
    CustomerUpdateDto,
    CustomerSearchDto,
    CustomerAnalyticsDto,
    CustomerDetailDto,
    CustomerPasswordChangeDto,
    AddressDto,
    AddressCreateDto,
    AddressUpdateDto,
    OrderListDto,
    UserPointDto,
    UserPointCreateDto,
    CouponDto,
    CouponCreateDto,
    CouponUpdateDto,
    CouponSummaryDto,
    CouponListDto
} from '@/app/[locale]/admin/customers/types';

// Query Keys
export const customerKeys = {
    all: ['customers'] as const,
    lists: () => [...customerKeys.all, 'list'] as const,
    list: (filters: CustomerSearchDto) => [...customerKeys.lists(), filters] as const,
    details: () => [...customerKeys.all, 'detail'] as const,
    detail: (id: string) => [...customerKeys.details(), id] as const,
    analytics: () => [...customerKeys.all, 'analytics'] as const,
};

// Get customers list
export function useCustomers(filters?: CustomerSearchDto) {
    return useQuery({
        queryKey: customerKeys.list(filters || {}),
        queryFn: async (): Promise<CustomerListDto[]> => {
            const params = new URLSearchParams();

            if (filters?.searchTerm) params.append('searchTerm', filters.searchTerm);
            if (filters?.email) params.append('email', filters.email);
            if (filters?.phoneNumber) params.append('phoneNumber', filters.phoneNumber);
            if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());
            if (filters?.createdAfter) params.append('createdAfter', filters.createdAfter);
            if (filters?.createdBefore) params.append('createdBefore', filters.createdBefore);
            if (filters?.minOrderAmount) params.append('minOrderAmount', filters.minOrderAmount.toString());
            if (filters?.maxOrderAmount) params.append('maxOrderAmount', filters.maxOrderAmount.toString());
            if (filters?.page) params.append('page', filters.page.toString());
            if (filters?.pageSize) params.append('pageSize', filters.pageSize.toString());
            if (filters?.sortBy) params.append('sortBy', filters.sortBy);
            if (filters?.sortDirection) params.append('sortDirection', filters.sortDirection);

            const queryString = params.toString();
            const url = queryString ? `/customer?${queryString}` : '/customer';

            return api.get<CustomerListDto[]>(url);
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Get customer by ID
export function useCustomer(id: string) {
    return useQuery({
        queryKey: customerKeys.detail(id),
        queryFn: (): Promise<CustomerDetailDto> => api.get<CustomerDetailDto>(`/customer/${id}`),
        enabled: !!id,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Get customer analytics
export function useCustomerAnalytics() {
    return useQuery({
        queryKey: customerKeys.analytics(),
        queryFn: (): Promise<CustomerAnalyticsDto> => api.get<CustomerAnalyticsDto>('/customer/analytics'),
        staleTime: 10 * 60 * 1000, // 10 minutes
    });
}

// Create customer
export function useCreateCustomer() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: CustomerCreateDto): Promise<{ customerId: string; message: string }> =>
            api.post<{ customerId: string; message: string }>('/customer', data),
        onSuccess: () => {
            // Invalidate and refetch customers list
            queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
            queryClient.invalidateQueries({ queryKey: customerKeys.analytics() });
        },
    });
}

// Update customer
export function useUpdateCustomer() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: CustomerUpdateDto): Promise<{ message: string }> =>
            api.put<{ message: string }>(`/customer/${data.id}`, data),
        onSuccess: (_, variables) => {
            // Invalidate and refetch customers list and specific customer
            queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.id) });
            queryClient.invalidateQueries({ queryKey: customerKeys.analytics() });
        },
    });
}

// Delete customer
export function useDeleteCustomer() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string): Promise<{ message: string }> =>
            api.delete<{ message: string }>(`/customer/${id}`),
        onSuccess: () => {
            // Invalidate and refetch customers list
            queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
            queryClient.invalidateQueries({ queryKey: customerKeys.analytics() });
        },
    });
}

// Toggle customer status
export function useToggleCustomerStatus() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/customer/${id}/toggle-status`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch customers list and specific customer
            queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables) });
            queryClient.invalidateQueries({ queryKey: customerKeys.analytics() });
        },
    });
}

// Change customer password
export function useChangeCustomerPassword() {
    return useMutation({
        mutationFn: (data: CustomerPasswordChangeDto): Promise<{ message: string }> =>
            api.post<{ message: string }>('/customer/change-password', data),
    });
}

// Search customers
export function useSearchCustomers() {
    return useMutation({
        mutationFn: (searchData: CustomerSearchDto): Promise<CustomerListDto[]> =>
            api.post<CustomerListDto[]>('/customer/search', searchData),
    });
}

// Address-related hooks
export const addressKeys = {
    all: ['addresses'] as const,
    lists: () => [...addressKeys.all, 'list'] as const,
    list: (customerId: string) => [...addressKeys.lists(), customerId] as const,
    details: () => [...addressKeys.all, 'detail'] as const,
    detail: (id: string) => [...addressKeys.details(), id] as const,
};

// Get customer addresses
export function useCustomerAddresses(customerId: string) {
    return useQuery({
        queryKey: addressKeys.list(customerId),
        queryFn: (): Promise<AddressDto[]> => api.get<AddressDto[]>(`/customer/${customerId}/addresses`),
        enabled: !!customerId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Create customer address
export function useCreateCustomerAddress() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, addressData }: { customerId: string; addressData: AddressCreateDto }): Promise<{ addressId: string; message: string }> =>
            api.post<{ addressId: string; message: string }>(`/customer/${customerId}/addresses`, addressData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch addresses list and customer details
            queryClient.invalidateQueries({ queryKey: addressKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Update customer address
export function useUpdateCustomerAddress() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, addressData }: { customerId: string; addressData: AddressUpdateDto }): Promise<{ message: string }> =>
            api.put<{ message: string }>(`/customer/${customerId}/addresses/${addressData.id}`, addressData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch addresses list and customer details
            queryClient.invalidateQueries({ queryKey: addressKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Delete customer address
export function useDeleteCustomerAddress() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, addressId }: { customerId: string; addressId: string }): Promise<{ message: string }> =>
            api.delete<{ message: string }>(`/customer/${customerId}/addresses/${addressId}`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch addresses list and customer details
            queryClient.invalidateQueries({ queryKey: addressKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Set address as default
export function useSetDefaultAddress() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, addressId }: { customerId: string; addressId: string }): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/customer/${customerId}/addresses/${addressId}/set-default`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch addresses list and customer details
            queryClient.invalidateQueries({ queryKey: addressKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Order-related hooks
export const orderKeys = {
    all: ['orders'] as const,
    lists: () => [...orderKeys.all, 'list'] as const,
    list: (customerId: string) => [...orderKeys.lists(), customerId] as const,
    details: () => [...orderKeys.all, 'detail'] as const,
    detail: (id: string) => [...orderKeys.details(), id] as const,
};

// Get customer orders
export function useCustomerOrders(customerId: string) {
    return useQuery({
        queryKey: orderKeys.list(customerId),
        queryFn: (): Promise<OrderListDto[]> => api.get<OrderListDto[]>(`/customer/${customerId}/orders`),
        enabled: !!customerId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Points-related hooks
export const pointKeys = {
    all: ['points'] as const,
    lists: () => [...pointKeys.all, 'list'] as const,
    list: (customerId: string) => [...pointKeys.lists(), customerId] as const,
    details: () => [...pointKeys.all, 'detail'] as const,
    detail: (id: string) => [...pointKeys.details(), id] as const,
};

// Get customer points
export function useCustomerPoints(customerId: string) {
    return useQuery({
        queryKey: pointKeys.list(customerId),
        queryFn: (): Promise<UserPointDto[]> => api.get<UserPointDto[]>(`/customer/${customerId}/points`),
        enabled: !!customerId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Add points to customer
export function useAddCustomerPoints() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (pointData: UserPointCreateDto): Promise<UserPointDto> =>
            api.post<UserPointDto>(`/customer/${pointData.customerId}/points/add`, pointData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch points list and customer details
            queryClient.invalidateQueries({ queryKey: pointKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Subtract points from customer
export function useSubtractCustomerPoints() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (pointData: UserPointCreateDto): Promise<UserPointDto> =>
            api.post<UserPointDto>(`/customer/${pointData.customerId}/points/subtract`, pointData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch points list and customer details
            queryClient.invalidateQueries({ queryKey: pointKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Coupon-related hooks
export const couponKeys = {
    all: ['coupons'] as const,
    lists: () => [...couponKeys.all, 'list'] as const,
    list: (customerId: string) => [...couponKeys.lists(), customerId] as const,
    details: () => [...couponKeys.all, 'detail'] as const,
    detail: (id: string) => [...couponKeys.details(), id] as const,
    summary: (customerId: string) => [...couponKeys.all, 'summary', customerId] as const,
};

// Get customer coupons
export function useCustomerCoupons(customerId: string) {
    return useQuery({
        queryKey: couponKeys.list(customerId),
        queryFn: (): Promise<CouponListDto[]> => api.get<CouponListDto[]>(`/coupon/customer/${customerId}`),
        enabled: !!customerId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Get customer coupon summary
export function useCustomerCouponSummary(customerId: string) {
    return useQuery({
        queryKey: couponKeys.summary(customerId),
        queryFn: (): Promise<CouponSummaryDto> => api.get<CouponSummaryDto>(`/coupon/customer/${customerId}/summary`),
        enabled: !!customerId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Create coupon for customer
export function useCreateCustomerCoupon() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (couponData: CouponCreateDto): Promise<{ couponId: string; message: string }> =>
            api.post<{ couponId: string; message: string }>('/coupon', couponData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Update customer coupon
export function useUpdateCustomerCoupon() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponData }: { customerId: string; couponData: CouponUpdateDto }): Promise<{ message: string }> =>
            api.put<{ message: string }>(`/coupon/${couponData.id}`, couponData),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Delete customer coupon
export function useDeleteCustomerCoupon() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponId }: { customerId: string; couponId: string }): Promise<{ message: string }> =>
            api.delete<{ message: string }>(`/coupon/${couponId}`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Extend customer coupon expiration
export function useExtendCustomerCouponExpiration() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponId, newExpirationDate }: { customerId: string; couponId: string; newExpirationDate: string }): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/coupon/${couponId}/extend-expiration`, { newExpirationDate }),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Reset customer coupon usage
export function useResetCustomerCouponUsage() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponId }: { customerId: string; couponId: string }): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/coupon/${couponId}/reset-usage`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Activate customer coupon
export function useActivateCustomerCoupon() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponId }: { customerId: string; couponId: string }): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/coupon/${couponId}/activate`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}

// Deactivate customer coupon
export function useDeactivateCustomerCoupon() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ customerId, couponId }: { customerId: string; couponId: string }): Promise<{ message: string }> =>
            api.post<{ message: string }>(`/coupon/${couponId}/deactivate`),
        onSuccess: (_, variables) => {
            // Invalidate and refetch coupons list and customer details
            queryClient.invalidateQueries({ queryKey: couponKeys.list(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: couponKeys.summary(variables.customerId) });
            queryClient.invalidateQueries({ queryKey: customerKeys.detail(variables.customerId) });
        },
    });
}