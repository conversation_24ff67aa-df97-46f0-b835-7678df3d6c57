'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { tr, enUS } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { api } from '@/lib/api/client';

interface AddCouponModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const couponSchema = z.object({
  couponCode: z.string().min(3, 'Kupon kodu en az 3 karakter olmalıdır').max(50),
  couponType: z.enum(['General', 'CustomerSpecific']),
  customerId: z.string().optional(),
  discountType: z.enum(['Percentage', 'FixedAmount']),
  discountValue: z.number().min(0.01, 'İndirim değeri 0\'dan büyük olmalıdır'),
  totalUsageLimit: z.number().optional(),
  usageLimitPerCustomer: z.number().min(1, 'Müşteri başına kullanım limiti en az 1 olmalıdır'),
  expirationDate: z.date().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type CouponFormData = z.infer<typeof couponSchema>;

export default function AddCouponModal({ isOpen, onClose, onSuccess }: AddCouponModalProps) {
  const t = useTranslations('admin.coupons');
  const locale = useLocale();
  const dateLocale = locale === 'tr' ? tr : enUS;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CouponFormData>({
    resolver: zodResolver(couponSchema),
    defaultValues: {
      couponType: 'General',
      discountType: 'Percentage',
      usageLimitPerCustomer: 1,
      isActive: true,
    },
  });

  const watchCouponType = form.watch('couponType');
  const watchDiscountType = form.watch('discountType');

  const onSubmit = async (data: CouponFormData) => {
    setIsSubmitting(true);
    try {
      const payload = {
        ...data,
        totalUsageLimit: data.totalUsageLimit || null,
        expirationDate: data.expirationDate?.toISOString() || null,
      };

      await api.post('/api/coupon', payload);
      toast.success(t('messages.addSuccess'));
      form.reset();
      onSuccess();
    } catch (error: any) {
      toast.error(error.response?.data?.message || t('messages.addError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateCouponCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue('couponCode', result);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('addModal.title')}</DialogTitle>
          <DialogDescription>{t('addModal.description')}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="couponCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.couponCode')}</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input {...field} placeholder="KUPON123" className="flex-1" />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateCouponCode}
                        className="shrink-0"
                      >
                        {t('form.generate')}
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="couponType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.couponType')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="General">{t('type.general')}</SelectItem>
                        <SelectItem value="CustomerSpecific">{t('type.customerSpecific')}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {watchCouponType === 'CustomerSpecific' && (
              <FormField
                control={form.control}
                name="customerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.customer')}</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('form.customerPlaceholder')} />
                    </FormControl>
                    <FormDescription>{t('form.customerDescription')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="discountType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.discountType')}</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Percentage">{t('discountType.percentage')}</SelectItem>
                        <SelectItem value="FixedAmount">{t('discountType.fixedAmount')}</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discountValue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {watchDiscountType === 'Percentage'
                        ? t('form.discountPercentage')
                        : t('form.discountAmount')
                      }
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        placeholder={watchDiscountType === 'Percentage' ? '10' : '50.00'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="totalUsageLimit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.totalUsageLimit')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder={t('form.unlimited')}
                      />
                    </FormControl>
                    <FormDescription>{t('form.totalUsageLimitDescription')}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="usageLimitPerCustomer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.usageLimitPerCustomer')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="expirationDate"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-3">
                  <FormLabel>{t('form.expirationDate')}</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value ? (
                            format(field.value, "PPP", { locale: dateLocale })
                          ) : (
                            <span>{t('form.selectDate')}</span>
                          )}
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date()}
                        initialFocus
                        locale={dateLocale}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>{t('form.expirationDateDescription')}</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.description')}</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder={t('form.descriptionPlaceholder')} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">{t('form.isActive')}</FormLabel>
                    <FormDescription>{t('form.isActiveDescription')}</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6">
              <Button type="button" variant="outline" onClick={onClose} className="w-full sm:w-auto">
                {t('actions.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {t('actions.save')}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
