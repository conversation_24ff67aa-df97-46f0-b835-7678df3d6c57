'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Plus,
  Minus,
  Star,
  TrendingUp,
  TrendingDown,
  Calendar,
  Loader2
} from 'lucide-react';
import { useCustomerPoints, useAddCustomerPoints, useSubtractCustomerPoints } from '@/hooks/api/useCustomers';
import { UserPointDto } from '../types';

// Form validation schema
const pointSchema = z.object({
  point: z.number().min(1, 'Puan miktarı en az 1 olmalıdır').max(10000, 'Puan miktarı en fazla 10000 olabilir'),
  description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
});

type PointFormData = z.infer<typeof pointSchema>;

interface CustomerPointsTabProps {
  customerId: string;
  currentBalance: number;
}

export function CustomerPointsTab({ customerId, currentBalance }: CustomerPointsTabProps) {
  const t = useTranslations("customer.points");
  const commonT = useTranslations("common");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isSubtractDialogOpen, setIsSubtractDialogOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { data: points, isLoading, error: fetchError } = useCustomerPoints(customerId);
  const addPointsMutation = useAddCustomerPoints();
  const subtractPointsMutation = useSubtractCustomerPoints();

  const {
    register: registerAdd,
    handleSubmit: handleSubmitAdd,
    reset: resetAdd,
    formState: { errors: errorsAdd },
  } = useForm<PointFormData>({
    resolver: zodResolver(pointSchema),
    defaultValues: {
      point: 0,
      description: '',
    },
  });

  const {
    register: registerSubtract,
    handleSubmit: handleSubmitSubtract,
    reset: resetSubtract,
    formState: { errors: errorsSubtract },
  } = useForm<PointFormData>({
    resolver: zodResolver(pointSchema),
    defaultValues: {
      point: 0,
      description: '',
    },
  });

  const getPointTypeText = (type: number) => {
    switch (type) {
      case 0:
        return t("types.pending");
      case 1:
        return t("types.earned");
      case 2:
        return t("types.cancelled");
      case 3:
        return t("types.spent");
      default:
        return "Unknown";
    }
  };

  const getPointTypeIcon = (type: number) => {
    switch (type) {
      case 0:
        return <Calendar className="mr-1 h-3 w-3" />;
      case 1:
        return <TrendingUp className="mr-1 h-3 w-3" />;
      case 2:
        return <Star className="mr-1 h-3 w-3" />;
      case 3:
        return <TrendingDown className="mr-1 h-3 w-3" />;
      default:
        return null;
    }
  };

  const getPointTypeBadgeVariant = (type: number) => {
    switch (type) {
      case 0:
        return "outline" as const;
      case 1:
        return "default" as const;
      case 2:
        return "secondary" as const;
      case 3:
        return "destructive" as const;
      default:
        return "outline" as const;
    }
  };

  const isPointPositive = (point: UserPointDto) => {
    return point.status !== 2 && point.status !== 3;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const onAddPoints = async (data: PointFormData) => {
    setError(null);
    try {
      await addPointsMutation.mutateAsync({
        customerId,
        point: data.point,
        description: data.description,
      });
      setIsAddDialogOpen(false);
      resetAdd();
    } catch (err: any) {
      setError(err.response?.data?.message || t("messages.addError"));
    }
  };

  const onSubtractPoints = async (data: PointFormData) => {
    setError(null);
    if (data.point > currentBalance) {
      setError(t("messages.insufficientPoints"));
      return;
    }
    try {
      await subtractPointsMutation.mutateAsync({
        customerId,
        point: data.point,
        description: data.description,
      });
      setIsSubtractDialogOpen(false);
      resetSubtract();
    } catch (err: any) {
      setError(err.response?.data?.message || t("messages.subtractError"));
    }
  };

  // Calculate summary statistics
  const pointSummary = {
    totalEarned: points?.filter(p => p.status === 1).reduce((sum, p) => sum + p.point, 0) || 0,
    totalSpent: points?.filter(p => p.status === 3).reduce((sum, p) => sum + p.point, 0) || 0,
    currentBalance: currentBalance,
    lastTransaction: points?.length ? points[0]?.createdAt : null
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{commonT("status.loading")}</span>
      </div>
    );
  }

  if (fetchError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {t("messages.fetchError")}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t("title")}</h3>
          <p className="text-sm text-muted-foreground">
            {points?.length || 0} işlem bulundu
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t("addPoints")}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("addPoints")}</DialogTitle>
                <DialogDescription>
                  Müşteriye puan ekleyin.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmitAdd(onAddPoints)} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="addPointAmount">{t("fields.pointAmount")} *</Label>
                  <Input
                    id="addPointAmount"
                    type="number"
                    {...registerAdd('point', { valueAsNumber: true })}
                    placeholder={t("placeholders.pointAmount")}
                  />
                  {errorsAdd.point && (
                    <p className="text-sm text-red-600">{errorsAdd.point.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="addDescription">{t("fields.description")}</Label>
                  <Textarea
                    id="addDescription"
                    {...registerAdd('description')}
                    placeholder={t("placeholders.description")}
                    rows={3}
                  />
                  {errorsAdd.description && (
                    <p className="text-sm text-red-600">{errorsAdd.description.message}</p>
                  )}
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    {commonT("actions.cancel")}
                  </Button>
                  <Button type="submit" disabled={addPointsMutation.isPending}>
                    {addPointsMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {commonT("status.saving")}
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        {t("addPoints")}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={isSubtractDialogOpen} onOpenChange={setIsSubtractDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Minus className="mr-2 h-4 w-4" />
                {t("subtractPoints")}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("subtractPoints")}</DialogTitle>
                <DialogDescription>
                  Müşteriden puan düşün. Mevcut bakiye: {currentBalance} puan
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmitSubtract(onSubtractPoints)} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="subtractPointAmount">{t("fields.pointAmount")} *</Label>
                  <Input
                    id="subtractPointAmount"
                    type="number"
                    max={currentBalance}
                    {...registerSubtract('point', { valueAsNumber: true })}
                    placeholder={t("placeholders.pointAmount")}
                  />
                  {errorsSubtract.point && (
                    <p className="text-sm text-red-600">{errorsSubtract.point.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subtractDescription">{t("fields.description")}</Label>
                  <Textarea
                    id="subtractDescription"
                    {...registerSubtract('description')}
                    placeholder={t("placeholders.description")}
                    rows={3}
                  />
                  {errorsSubtract.description && (
                    <p className="text-sm text-red-600">{errorsSubtract.description.message}</p>
                  )}
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsSubtractDialogOpen(false)}>
                    {commonT("actions.cancel")}
                  </Button>
                  <Button type="submit" variant="destructive" disabled={subtractPointsMutation.isPending}>
                    {subtractPointsMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {commonT("status.saving")}
                      </>
                    ) : (
                      <>
                        <Minus className="mr-2 h-4 w-4" />
                        {t("subtractPoints")}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Point Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t("summary.currentBalance")}</span>
            </div>
            <p className="text-2xl font-bold mt-2 text-blue-600">{pointSummary.currentBalance}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">{t("summary.totalEarned")}</span>
            </div>
            <p className="text-2xl font-bold mt-2 text-green-600">{pointSummary.totalEarned}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">{t("summary.totalSpent")}</span>
            </div>
            <p className="text-2xl font-bold mt-2 text-red-600">{pointSummary.totalSpent}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t("summary.lastTransaction")}</span>
            </div>
            <p className="text-sm font-semibold mt-2">
              {pointSummary.lastTransaction ? formatDate(pointSummary.lastTransaction) : '-'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Points History Table */}
      {!points || points.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Star className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">{t("noPoints")}</h3>
            <p className="text-muted-foreground text-center mb-4">
              Bu müşteriye ait henüz puan hareketi bulunmuyor.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              {t("addPoints")}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              {t("pointHistory")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("fields.pointType")}</TableHead>
                    <TableHead>{t("fields.pointAmount")}</TableHead>
                    <TableHead>{t("fields.description")}</TableHead>
                    <TableHead>{t("fields.createdAt")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {points.map((point) => (
                    <TableRow key={point.id}>
                      <TableCell>
                        <Badge variant={getPointTypeBadgeVariant(point.status)}>
                          {getPointTypeIcon(point.status)}
                          {getPointTypeText(point.status)}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        <span className={isPointPositive(point) ? "text-green-600" : "text-red-600"}>
                          {isPointPositive(point) ? '+' : ''}{point.point}
                        </span>
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {point.description || '-'}
                      </TableCell>
                      <TableCell>
                        {formatDate(point.createdAt)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
