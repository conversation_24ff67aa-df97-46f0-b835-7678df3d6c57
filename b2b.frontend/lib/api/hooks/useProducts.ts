'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../client';

// Types - Backend ProductDto ile uyumlu
export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productType: number;
  categoryId?: string;
  brandId?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  stockQuantity?: number;
  createdAt?: string;
  updatedAt?: string;
}

// Detailed ProductDto for single product fetch
export interface ProductDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productType: number;
  isActive: boolean;
  isDeleted: boolean;
  parent?: ProductDto;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  brand?: {
    id: string;
    name: string;
    description?: string;
  };
  sku?: string;
  barcode?: string;
  price?: number;
  discountedPrice?: number;
  stockQuantity?: number;
  pointValue?: number;
  createdAt?: string;
  updatedAt?: string;
  variants?: ProductDto[];
  images?: {
    id: string;
    productId: string;
    originalImagePath: string;
    thumbnailMediumPath: string;
    thumbnailSmallPath: string;
    isMainImage: boolean;
    sortOrder: number;
    altText: string;
  }[];
  faqs?: {
    id: string;
    productId: string;
    question: string;
    answer: string;
    sortOrder: number;
  }[];
  reviews?: any[];
  attributeMappings?: {
    id: string;
    productId: string;
    attributeId: string;
    attributeValueId: string;
    attribute: {
      id: string;
      name: string;
      shortName: string;
      isVariantAttribute: boolean;
      isListAttribute: boolean;
    };
    attributeValue: {
      id: string;
      attributeId: string;
      value: string;
    };
  }[];
  seo?: {
    id?: string;
    productId?: string;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    structuredData?: string;
    canonicalUrl?: string;
    noIndex?: boolean;
    noFollow?: boolean;
  };
}

// Product creation için backend ProductCreateDto ile uyumlu
export interface ProductCreateDto {
  name: string;
  description?: string;
  productType?: number;
  categoryId?: string;
  brandId?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  discountedPrice?: number;
  pointValue?: number;
  stockQuantity?: number;
  attributeMappings?: any[];
  images?: any[];
  faqs?: any[];
  seo?: any;
}

// Product update için backend ProductUpdateDto ile uyumlu
export interface ProductUpdateDto {
  id: string;
  name: string;
  description?: string;
  productType?: number;
  categoryId?: string;
  brandId?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  discountedPrice?: number;
  stockQuantity?: number;
  pointValue?: number;
  isActive?: boolean;
  attributeMappings?: any[];
  images?: any[];
  faqs?: any[];
  seo?: any;
  variants?: any[];
}

export interface ProductFilter {
  search?: string;
  category?: string;
  status?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Fetch products with optional filtering
export const useProducts = (filters: ProductFilter = {}) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => api.get<Product[]>('/product', filters),
  });
};

// Fetch a single product by ID
export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => api.get<ProductDto>(`/product/${id}`),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

// Fetch products for dropdown
export const useProductsDropdown = () => {
  return useQuery({
    queryKey: ['products-dropdown'],
    queryFn: () => api.get<Array<{ id: string; name: string }>>('/product/dropdown'),
  });
};

// Server-side function moved to lib/api/server/products.ts

// Create a new product
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (newProduct: ProductCreateDto) =>
      api.post<{ message: string; productId: string }>('/product', newProduct),
    onSuccess: () => {
      // Invalidate the products query to refetch the updated list
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Update an existing product
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProductUpdateDto) =>
      api.put<{ message: string; productId: string }>(`/product/${data.id}`, data),
    onSuccess: (_, variables) => {
      // Invalidate the product query to refetch the updated data
      queryClient.invalidateQueries({ queryKey: ['product', variables.id] });
      // Invalidate the products list query
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

// Delete a product
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => api.delete<void>(`/product/${id}`),
    onSuccess: (_, id) => {
      // Remove the product from the cache
      queryClient.removeQueries({ queryKey: ['product', id] });
      // Invalidate the products list query
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};
