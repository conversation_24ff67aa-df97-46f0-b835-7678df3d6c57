{"product": {"title": "Products", "single": "Product", "details": "Product Details", "list": "Product List", "add": "Add Product", "edit": "Edit Product", "editProduct": "Edit Product", "editProductDescription": "Edit and update existing product", "delete": "Delete Product", "noProducts": "No products found", "fields": {"name": "Name", "description": "Description", "price": "Price", "discountedPrice": "Discounted Price", "stock": "Stock", "category": "Category", "brand": "Brand", "sku": "SKU", "barcode": "Barcode", "images": "Images", "status": "Status", "variants": "Variant Count", "productType": "Product Type", "pointValue": "Point Value"}, "status": {"active": "Active", "inactive": "Inactive", "outOfStock": "Out of Stock"}, "filters": {"title": "Filters", "price": "Price Range", "category": "Category", "brand": "Brand", "status": "Status", "apply": "Apply Filters", "clear": "Clear Filters", "search": "Search...", "min": "Min", "max": "Max", "minStock": "<PERSON>", "maxStock": "<PERSON>"}, "sort": {"title": "Sort By", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "nameAsc": "Name: A to Z", "nameDesc": "Name: Z to A", "newest": "Newest First", "oldest": "Oldest First"}, "productType": {"Simple": "Simple", "Variant": "<PERSON><PERSON><PERSON>", "Grouped": "Grouped"}, "viewMode": {"table": "Table View", "card": "Card View"}, "validation": {"nameRequired": "Product name is required.", "productTypeRequired": "Product type is required.", "skuRequired": "SKU is required.", "categoryRequired": "Category selection is required.", "priceRequired": "Price is required and must be greater than 0.", "stockRequired": "Stock quantity is required and must be greater than 0.", "slugRequired": "Slug is required."}, "faq": {"deleteFAQ": "You are about to delete the question", "deleteFAQConfirmation": "Are you sure you want to delete this question?", "addFAQ": "Add Question", "editFAQ": "Edit Question", "updateFAQ": "Update Question", "question": "Question", "answer": "Answer", "editProductFAQ": "Edit Product FAQ", "addProductFAQDescription": "Add a new question to the product FAQ.", "addProductFAQ": "Add Question to Product FAQ", "editProductFAQDescription": "Edit the question in the product FAQ."}, "combobox": {"selectCategory": "Select category", "selectBrand": "Select brand", "searchCategories": "Search categories...", "searchBrands": "Search brands...", "noResults": "No results found", "itemsSelected": "items selected"}, "form": {"productInfo": "Product Information", "productInfoDesc": "Basic product information and details", "productImages": "Product Images", "basicInfo": "Basic Information", "productName": "Product Name", "productNamePlaceholder": "Enter product name", "sku": "SKU", "skuPlaceholder": "Enter SKU code", "description": "Description", "descriptionPlaceholder": "Enter product description", "categoryAndBrand": "Category and Brand", "category": "Category", "selectCategory": "Select category", "brand": "Brand", "selectBrand": "Select brand", "price": "Price", "stock": "Stock", "barcode": "Barcode", "barcodePlaceholder": "Enter barcode number", "priceAndStock": "Price and Stock", "statusAndDates": "Status and Dates", "saveChanges": "Save Changes", "normalPrice": "Normal Price (₺)", "discountPrice": "Discount Price (₺)", "createdAt": "Created At", "lastUpdate": "Last Update"}, "tabs": {"details": "Details", "attributes": "Attributes", "specifications": "Attributes", "variants": "Variants", "seo": "SEO", "faq": "FAQ", "reviews": "Reviews"}, "attributes": {"title": "Attribute", "value": "Value", "actions": "Actions", "addattribute": "Add Attribute"}, "specifications": {"title": "Specification Name", "value": "Specification Value", "actions": "Actions", "addSpecification": "Add Specification"}, "variants": {"variantType": "Variant Type", "value": "Value", "stock": "Stock", "actions": "Actions", "addVariant": "<PERSON><PERSON>"}, "seo": {"title": "SEO Title", "metaDescription": "Meta Description", "keywords": "Keywords", "keywordsPlaceholder": "Keywords (separated by commas)", "ogImage": "Open Graph Image", "changeOgImage": "Change OG Image", "structuredData": "Structured Data (LD+JSON)"}, "reviews": {"addReview": "Add Review", "averageRating": "Average Rating", "outOf5": "/ 5"}, "actions": {"actions": "Actions", "edit": "Edit", "delete": "Delete", "backToProducts": "Back to Products", "updatePrice": "Update Price", "updateStock": "Update Stock", "viewProductPage": "View Product Page", "salesReports": "Sales Reports", "deleteProduct": "Delete Product"}, "placeholders": {"seoTitle": "SEO title", "metaDescription": "Meta description"}, "description": "Description", "addImage": "Add Image"}, "variant": {"addProductVariant": "Add Product Variant", "editProductVariant": "Edit Product Variant", "selectVariantTypeAndOption": "Select the variant type and option you want to add.", "editVariantDescription": "Edit and update variant information.", "variantType": "Variant Type", "option": "Option", "selectVariantType": "Select variant type", "selectOption": "Select option", "skuPlaceholder": "Will be generated automatically", "stock": "Stock", "price": "Price", "salePrice": "Sale Price", "optional": "Optional", "active": "Active", "updateVariant": "Update <PERSON><PERSON><PERSON>"}}