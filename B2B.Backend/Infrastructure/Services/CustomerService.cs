using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;

namespace Infrastructure.Services;

public class CustomerService : ICustomerService
{
    private readonly ICustomerRepository _customerRepository;
    private readonly IEncryptionService _encryptionService;
    private readonly IPasswordHasher<Customer> _passwordHasher;
    private readonly IGenericRepository<Order> _orderRepository;
    private readonly IGenericRepository<UserPoint> _pointRepository;
    private readonly IGenericRepository<Coupon> _couponRepository;
    private readonly IGenericRepository<Address> _addressRepository;

    public CustomerService(
        ICustomerRepository customerRepository,
        IEncryptionService encryptionService,
        IPasswordHasher<Customer> passwordHasher,
        IGenericRepository<Order> orderRepository,
        IGenericRepository<UserPoint> pointRepository,
        IGenericRepository<Coupon> couponRepository,
        IGenericRepository<Address> addressRepository)
    {
        _customerRepository = customerRepository;
        _encryptionService = encryptionService;
        _passwordHasher = passwordHasher;
        _orderRepository = orderRepository;
        _pointRepository = pointRepository;
        _couponRepository = couponRepository;
        _addressRepository = addressRepository;
    }

    public async Task<List<CustomerListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var customers = await _customerRepository.GetPagedAsync(page, pageSize);
        var result = new List<CustomerListDto>();

        foreach (var customer in customers.Where(c => !c.IsDeleted))
        {
            var orderCount = await _customerRepository.GetCustomerOrderCountAsync(customer.Id);
            var totalSpent = await _customerRepository.GetCustomerTotalSpentAsync(customer.Id);
            var lastOrder = await _orderRepository.Query()
                .Where(o => o.CustomerId == customer.Id && !o.IsDeleted)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync();

            result.Add(new CustomerListDto
            {
                Id = customer.Id,
                NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
                Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
                PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
                IsActive = customer.IsActive,
                CreatedAt = customer.CreatedAt,
                OrderCount = orderCount,
                TotalOrderAmount = totalSpent,
                LastOrderDate = lastOrder?.CreatedAt
            });
        }

        return result.OrderByDescending(c => c.CreatedAt).ToList();
    }

    public async Task<CustomerDto?> GetByIdAsync(Guid id)
    {
        var customer = await _customerRepository.GetByIdAsync(id);
        if (customer == null || customer.IsDeleted)
            return null;

        return await MapToCustomerDto(customer);
    }

    public async Task<CustomerDetailDto?> GetDetailByIdAsync(Guid id)
    {
        var customer = await _customerRepository.GetWithAllRelatedDataAsync(id);
        if (customer == null || customer.IsDeleted)
            return null;

        var orderCount = customer.Orders.Count(o => !o.IsDeleted);
        var totalSpent = customer.Orders.Where(o => !o.IsDeleted).Sum(o => o.TotalAmount);
        var lastOrder = customer.Orders.Where(o => !o.IsDeleted).OrderByDescending(o => o.CreatedAt).FirstOrDefault();

        return new CustomerDetailDto
        {
            Id = customer.Id,
            NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
            PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
            Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
            TaxOrIdentityNumber = _encryptionService.DecryptIfNotEmpty(customer.TaxOrIdentityNumber),
            TaxOffice = customer.TaxOffice,
            IsActive = customer.IsActive,
            CreatedAt = customer.CreatedAt,
            UpdatedAt = customer.UpdatedAt,
            
            // Statistics
            AddressCount = customer.Addresses.Count(a => !a.IsDeleted),
            OrderCount = orderCount,
            TotalOrderAmount = totalSpent,
            PointBalance = customer.Points.OrderByDescending(p => p.CreatedAt).FirstOrDefault()?.Balance ?? 0,
            CouponCount = customer.Coupons.Count(c => !c.IsDeleted),
            ReviewCount = customer.Reviews.Count(r => !r.IsDeleted),
            FavouriteCount = customer.Favourites.Count(f => !f.IsDeleted),
            LastOrderDate = lastOrder?.CreatedAt,
            
            // Related data - will be populated separately if needed
            Addresses = [],
            RecentOrders = [],
            RecentPoints = [],
            ActiveCoupons = []
        };
    }

    public async Task<Guid> CreateAsync(CustomerCreateDto dto)
    {
        // Check for existing email
        if (!string.IsNullOrEmpty(dto.Email))
        {
            var encryptedEmail = _encryptionService.Encrypt(dto.Email);
            if (await _customerRepository.IsEmailExistsAsync(encryptedEmail))
                throw new InvalidOperationException("Bu email adresi zaten kullanılıyor.");
        }

        // Check for existing phone
        if (!string.IsNullOrEmpty(dto.PhoneNumber))
        {
            var encryptedPhone = _encryptionService.Encrypt(dto.PhoneNumber);
            if (await _customerRepository.IsPhoneExistsAsync(encryptedPhone))
                throw new InvalidOperationException("Bu telefon numarası zaten kullanılıyor.");
        }

        // Check for existing tax/identity number
        if (!string.IsNullOrEmpty(dto.TaxOrIdentityNumber))
        {
            var encryptedTaxId = _encryptionService.Encrypt(dto.TaxOrIdentityNumber);
            if (await _customerRepository.IsTaxOrIdentityNumberExistsAsync(encryptedTaxId))
                throw new InvalidOperationException("Bu kimlik/vergi numarası zaten kullanılıyor.");
        }

        // KVKK onayı kontrolü
        if (!dto.AcceptedKvkk)
            throw new InvalidOperationException("KVKK onayı gereklidir.");

        if (!dto.AcceptedMembershipAgreement)
            throw new InvalidOperationException("Üyelik Sözleşmesi onayı gereklidir.");

        var currentTime = DateTime.UtcNow;
        var customer = new Customer
        {
            Id = Guid.CreateVersion7(),
            NameSurname = _encryptionService.Encrypt(dto.NameSurname),
            PhoneNumber = _encryptionService.EncryptIfNotEmpty(dto.PhoneNumber),
            Email = _encryptionService.EncryptIfNotEmpty(dto.Email),
            TaxOrIdentityNumber = _encryptionService.EncryptIfNotEmpty(dto.TaxOrIdentityNumber),
            TaxOffice = dto.TaxOffice,
            AcceptedKvkk = dto.AcceptedKvkk,
            AcceptedMembershipAgreement = dto.AcceptedMembershipAgreement,
            KvkkAcceptedAt = dto.AcceptedKvkk ? currentTime : null,
            MembershipAgreementAcceptedAt = dto.AcceptedMembershipAgreement ? currentTime : null,
            IsActive = dto.IsActive,
            CreatedAt = currentTime,
            UpdatedAt = currentTime
        };

        // Hash password properly using ASP.NET Core Identity's password hasher
        customer.Password = _passwordHasher.HashPassword(customer, dto.Password);

        // Create cart for customer
        customer.Cart = new Cart
        {
            Id = Guid.CreateVersion7(),
            CustomerId = customer.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        customer.CartId = customer.Cart.Id;

        await _customerRepository.AddAsync(customer);
        await _customerRepository.SaveChangesAsync();

        // Add to history
        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Created, Guid.Empty); // TODO: Get actual employee ID
        await _customerRepository.SaveChangesAsync();

        return customer.Id;
    }

    private async Task<CustomerDto> MapToCustomerDto(Customer customer)
    {
        var orderCount = await _customerRepository.GetCustomerOrderCountAsync(customer.Id);
        var totalSpent = await _customerRepository.GetCustomerTotalSpentAsync(customer.Id);
        var pointBalance = customer.Points.OrderByDescending(p => p.CreatedAt).FirstOrDefault()?.Balance ?? 0;
        var couponCount = await _couponRepository.CountAsync(c => c.CustomerId == customer.Id && !c.IsDeleted);
        var addressCount = await _addressRepository.CountAsync(a => a.CustomerId == customer.Id && !a.IsDeleted);

        return new CustomerDto
        {
            Id = customer.Id,
            NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
            PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
            Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
            TaxOrIdentityNumber = _encryptionService.DecryptIfNotEmpty(customer.TaxOrIdentityNumber),
            TaxOffice = customer.TaxOffice,
            IsActive = customer.IsActive,
            CreatedAt = customer.CreatedAt,
            UpdatedAt = customer.UpdatedAt,
            AddressCount = addressCount,
            OrderCount = orderCount,
            TotalOrderAmount = totalSpent,
            PointBalance = pointBalance,
            CouponCount = couponCount
        };
    }

    public async Task UpdateAsync(CustomerUpdateDto dto)
    {
        var customer = await _customerRepository.GetByIdAsync(dto.Id);
        if (customer == null || customer.IsDeleted)
            throw new ArgumentException("Customer not found");

        // Check for existing email (exclude current customer)
        if (!string.IsNullOrEmpty(dto.Email))
        {
            var encryptedEmail = _encryptionService.Encrypt(dto.Email);
            if (await _customerRepository.IsEmailExistsAsync(encryptedEmail, dto.Id))
                throw new InvalidOperationException("Bu email adresi zaten kullanılıyor.");
        }

        // Check for existing phone (exclude current customer)
        if (!string.IsNullOrEmpty(dto.PhoneNumber))
        {
            var encryptedPhone = _encryptionService.Encrypt(dto.PhoneNumber);
            if (await _customerRepository.IsPhoneExistsAsync(encryptedPhone, dto.Id))
                throw new InvalidOperationException("Bu telefon numarası zaten kullanılıyor.");
        }

        // Check for existing tax/identity number (exclude current customer)
        if (!string.IsNullOrEmpty(dto.TaxOrIdentityNumber))
        {
            var encryptedTaxId = _encryptionService.Encrypt(dto.TaxOrIdentityNumber);
            if (await _customerRepository.IsTaxOrIdentityNumberExistsAsync(encryptedTaxId, dto.Id))
                throw new InvalidOperationException("Bu kimlik/vergi numarası zaten kullanılıyor.");
        }

        // Update customer properties
        customer.NameSurname = _encryptionService.Encrypt(dto.NameSurname);
        customer.PhoneNumber = _encryptionService.EncryptIfNotEmpty(dto.PhoneNumber);
        customer.Email = _encryptionService.EncryptIfNotEmpty(dto.Email);
        customer.TaxOrIdentityNumber = _encryptionService.EncryptIfNotEmpty(dto.TaxOrIdentityNumber);
        customer.TaxOffice = dto.TaxOffice;
        customer.IsActive = dto.IsActive;
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        // Add to history
        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty); // TODO: Get actual employee ID
        await _customerRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var customer = await _customerRepository.GetByIdAsync(id);
        if (customer == null || customer.IsDeleted)
            throw new ArgumentException("Customer not found");

        customer.IsDeleted = true;
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        // Add to history
        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Deleted, Guid.Empty); // TODO: Get actual employee ID
        await _customerRepository.SaveChangesAsync();
    }

    public async Task<bool> ChangePasswordAsync(CustomerPasswordChangeDto dto)
    {
        var customer = await _customerRepository.GetByIdAsync(dto.CustomerId);
        if (customer == null || customer.IsDeleted)
            return false;

        // Hash password properly using ASP.NET Core Identity's password hasher
        customer.Password = _passwordHasher.HashPassword(customer, dto.NewPassword);
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        // Add to history
        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty); // TODO: Get actual employee ID
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> VerifyPasswordAsync(Guid customerId, string password)
    {
        var customer = await _customerRepository.GetByIdAsync(customerId);
        if (customer == null || customer.IsDeleted)
            return false;

        // Verify password using ASP.NET Core Identity's password hasher
        var result = _passwordHasher.VerifyHashedPassword(customer, customer.Password, password);
        return result == PasswordVerificationResult.Success || result == PasswordVerificationResult.SuccessRehashNeeded;
    }

    public async Task<List<CustomerListDto>> SearchAsync(CustomerSearchDto searchDto)
    {
        var query = _customerRepository.Query().Where(c => !c.IsDeleted);

        // Apply filters
        if (!string.IsNullOrEmpty(searchDto.SearchTerm))
        {
            // Note: For encrypted fields, we need to decrypt and search, which is not efficient
            // In production, consider using searchable encryption or separate search index
            var allCustomers = await query.ToListAsync();
            var filteredCustomers = allCustomers.Where(c =>
                _encryptionService.DecryptIfNotEmpty(c.NameSurname)?.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                _encryptionService.DecryptIfNotEmpty(c.Email)?.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                _encryptionService.DecryptIfNotEmpty(c.PhoneNumber)?.Contains(searchDto.SearchTerm, StringComparison.OrdinalIgnoreCase) == true
            ).ToList();

            return await ConvertToCustomerListDto(filteredCustomers, searchDto);
        }

        if (searchDto.IsActive.HasValue)
            query = query.Where(c => c.IsActive == searchDto.IsActive.Value);

        if (searchDto.CreatedAfter.HasValue)
            query = query.Where(c => c.CreatedAt >= searchDto.CreatedAfter.Value);

        if (searchDto.CreatedBefore.HasValue)
            query = query.Where(c => c.CreatedAt <= searchDto.CreatedBefore.Value);

        // Apply sorting
        query = searchDto.SortDirection?.ToLower() == "asc"
            ? query.OrderBy(c => c.CreatedAt)
            : query.OrderByDescending(c => c.CreatedAt);

        // Apply pagination
        if (searchDto.Page.HasValue && searchDto.PageSize.HasValue)
        {
            query = query.Skip((searchDto.Page.Value - 1) * searchDto.PageSize.Value)
                         .Take(searchDto.PageSize.Value);
        }

        var customers = await query.ToListAsync();
        return await ConvertToCustomerListDto(customers, searchDto);
    }

    private async Task<List<CustomerListDto>> ConvertToCustomerListDto(List<Customer> customers, CustomerSearchDto? searchDto = null)
    {
        var result = new List<CustomerListDto>();

        foreach (var customer in customers)
        {
            var orderCount = await _customerRepository.GetCustomerOrderCountAsync(customer.Id);
            var totalSpent = await _customerRepository.GetCustomerTotalSpentAsync(customer.Id);

            // Apply amount filters if specified
            if (searchDto?.MinOrderAmount.HasValue == true && totalSpent < searchDto.MinOrderAmount.Value)
                continue;
            if (searchDto?.MaxOrderAmount.HasValue == true && totalSpent > searchDto.MaxOrderAmount.Value)
                continue;

            var lastOrder = await _orderRepository.Query()
                .Where(o => o.CustomerId == customer.Id && !o.IsDeleted)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync();

            result.Add(new CustomerListDto
            {
                Id = customer.Id,
                NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
                Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
                PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
                IsActive = customer.IsActive,
                CreatedAt = customer.CreatedAt,
                OrderCount = orderCount,
                TotalOrderAmount = totalSpent,
                LastOrderDate = lastOrder?.CreatedAt
            });
        }

        return result;
    }

    public async Task<CustomerDto?> GetByEmailAsync(string email)
    {
        var encryptedEmail = _encryptionService.Encrypt(email);
        var customer = await _customerRepository.GetByEmailAsync(encryptedEmail);
        return customer != null ? await MapToCustomerDto(customer) : null;
    }

    public async Task<CustomerDto?> GetByPhoneAsync(string phoneNumber)
    {
        var encryptedPhone = _encryptionService.Encrypt(phoneNumber);
        var customer = await _customerRepository.GetByPhoneAsync(encryptedPhone);
        return customer != null ? await MapToCustomerDto(customer) : null;
    }

    public async Task<bool> IsEmailExistsAsync(string email)
    {
        var encryptedEmail = _encryptionService.Encrypt(email);
        return await _customerRepository.IsEmailExistsAsync(encryptedEmail);
    }

    public async Task<bool> IsPhoneExistsAsync(string phoneNumber)
    {
        var encryptedPhone = _encryptionService.Encrypt(phoneNumber);
        return await _customerRepository.IsPhoneExistsAsync(encryptedPhone);
    }

    public async Task<bool> ActivateAsync(Guid id)
    {
        var customer = await _customerRepository.GetByIdAsync(id);
        if (customer == null || customer.IsDeleted)
            return false;

        customer.IsActive = true;
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty);
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeactivateAsync(Guid id)
    {
        var customer = await _customerRepository.GetByIdAsync(id);
        if (customer == null || customer.IsDeleted)
            return false;

        customer.IsActive = false;
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty);
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> ToggleStatusAsync(Guid id)
    {
        var customer = await _customerRepository.GetByIdAsync(id);
        if (customer == null || customer.IsDeleted)
            return false;

        customer.IsActive = !customer.IsActive;
        customer.UpdatedAt = DateTime.UtcNow;

        _customerRepository.Update(customer);
        await _customerRepository.SaveChangesAsync();

        await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty);
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<CustomerAnalyticsDto> GetAnalyticsAsync()
    {
        var totalCustomers = await _customerRepository.GetTotalCustomersCountAsync();
        var activeCustomers = await _customerRepository.GetActiveCustomersCountAsync();
        var inactiveCustomers = totalCustomers - activeCustomers;

        var thisMonth = DateTime.UtcNow.Date.AddDays(1 - DateTime.UtcNow.Day);
        var lastMonth = thisMonth.AddMonths(-1);

        var newCustomersThisMonth = await _customerRepository.GetNewCustomersCountAsync(thisMonth, DateTime.UtcNow);
        var newCustomersLastMonth = await _customerRepository.GetNewCustomersCountAsync(lastMonth, thisMonth.AddDays(-1));

        var allOrders = await _orderRepository.Query().Where(o => !o.IsDeleted).ToListAsync();
        var averageOrderAmount = allOrders.Any() ? allOrders.Average(o => o.TotalAmount) : 0;
        var totalCustomerValue = allOrders.Sum(o => o.TotalAmount);

        var registrationsByMonth = await GetRegistrationsByMonthAsync(12);
        var topSpenders = await GetTopSpendersAsync(10);

        return new CustomerAnalyticsDto
        {
            TotalCustomers = totalCustomers,
            ActiveCustomers = activeCustomers,
            InactiveCustomers = inactiveCustomers,
            NewCustomersThisMonth = newCustomersThisMonth,
            NewCustomersLastMonth = newCustomersLastMonth,
            AverageOrderAmount = averageOrderAmount,
            TotalCustomerValue = totalCustomerValue,
            RegistrationsByMonth = registrationsByMonth,
            TopSpenders = topSpenders
        };
    }

    public async Task<List<CustomerTopSpendersDto>> GetTopSpendersAsync(int count = 10)
    {
        var customers = await _customerRepository.Query()
            .Where(c => !c.IsDeleted)
            .ToListAsync();

        var topSpenders = new List<CustomerTopSpendersDto>();

        foreach (var customer in customers)
        {
            var totalSpent = await _customerRepository.GetCustomerTotalSpentAsync(customer.Id);
            var orderCount = await _customerRepository.GetCustomerOrderCountAsync(customer.Id);

            if (totalSpent > 0)
            {
                topSpenders.Add(new CustomerTopSpendersDto
                {
                    CustomerId = customer.Id,
                    CustomerName = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
                    Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
                    TotalSpent = totalSpent,
                    OrderCount = orderCount
                });
            }
        }

        return topSpenders.OrderByDescending(ts => ts.TotalSpent).Take(count).ToList();
    }

    public async Task<List<CustomerRegistrationByMonthDto>> GetRegistrationsByMonthAsync(int months = 12)
    {
        var startDate = DateTime.UtcNow.AddMonths(-months).Date.AddDays(1 - DateTime.UtcNow.Day);
        var customers = await _customerRepository.GetCustomersByDateRangeAsync(startDate, DateTime.UtcNow);

        var registrations = customers
            .GroupBy(c => new { c.CreatedAt.Year, c.CreatedAt.Month })
            .Select(g => new CustomerRegistrationByMonthDto
            {
                Year = g.Key.Year,
                Month = g.Key.Month,
                MonthName = new DateTime(g.Key.Year, g.Key.Month, 1).ToString("MMMM yyyy"),
                Count = g.Count()
            })
            .OrderBy(r => r.Year)
            .ThenBy(r => r.Month)
            .ToList();

        return registrations;
    }

    public async Task<int> GetTotalCustomersAsync()
    {
        return await _customerRepository.GetTotalCustomersCountAsync();
    }

    public async Task<int> GetActiveCustomersAsync()
    {
        return await _customerRepository.GetActiveCustomersCountAsync();
    }

    public async Task<int> GetNewCustomersCountAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var start = startDate ?? DateTime.UtcNow.Date.AddDays(1 - DateTime.UtcNow.Day);
        var end = endDate ?? DateTime.UtcNow;
        return await _customerRepository.GetNewCustomersCountAsync(start, end);
    }

    public async Task<decimal> GetCustomerTotalSpentAsync(Guid customerId)
    {
        return await _customerRepository.GetCustomerTotalSpentAsync(customerId);
    }

    public async Task<int> GetCustomerOrderCountAsync(Guid customerId)
    {
        return await _customerRepository.GetCustomerOrderCountAsync(customerId);
    }

    public async Task<bool> BulkActivateAsync(List<Guid> customerIds)
    {
        var customers = await _customerRepository.Query()
            .Where(c => customerIds.Contains(c.Id) && !c.IsDeleted)
            .ToListAsync();

        foreach (var customer in customers)
        {
            customer.IsActive = true;
            customer.UpdatedAt = DateTime.UtcNow;
            await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty);
        }

        _customerRepository.UpdateRange(customers);
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> BulkDeactivateAsync(List<Guid> customerIds)
    {
        var customers = await _customerRepository.Query()
            .Where(c => customerIds.Contains(c.Id) && !c.IsDeleted)
            .ToListAsync();

        foreach (var customer in customers)
        {
            customer.IsActive = false;
            customer.UpdatedAt = DateTime.UtcNow;
            await _customerRepository.AddToHistoryAsync(customer, ChangeType.Updated, Guid.Empty);
        }

        _customerRepository.UpdateRange(customers);
        await _customerRepository.SaveChangesAsync();

        return true;
    }

    public async Task<bool> BulkDeleteAsync(List<Guid> customerIds)
    {
        var customers = await _customerRepository.Query()
            .Where(c => customerIds.Contains(c.Id) && !c.IsDeleted)
            .ToListAsync();

        foreach (var customer in customers)
        {
            customer.IsDeleted = true;
            customer.UpdatedAt = DateTime.UtcNow;
            await _customerRepository.AddToHistoryAsync(customer, ChangeType.Deleted, Guid.Empty);
        }

        _customerRepository.UpdateRange(customers);
        await _customerRepository.SaveChangesAsync();

        return true;
    }
}
