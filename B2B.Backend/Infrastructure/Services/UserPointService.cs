using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class UserPointService : IUserPointService
{
    private readonly IUserPointRepository _userPointRepository;
    private readonly IEncryptionService _encryptionService;

    public UserPointService(
        IUserPointRepository userPointRepository,
        IEncryptionService encryptionService)
    {
        _userPointRepository = userPointRepository;
        _encryptionService = encryptionService;
    }

    public async Task<List<UserPointListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var userPoints = await _userPointRepository.GetPagedAsync(page, pageSize);
        var result = new List<UserPointListDto>();

        foreach (var userPoint in userPoints.Where(up => !up.IsDeleted))
        {
            result.Add(MapToUserPointListDto(userPoint));
        }

        return result.OrderByDescending(up => up.CreatedAt).ToList();
    }

    public async Task<UserPointDto?> GetByIdAsync(Guid id)
    {
        var userPoint = await _userPointRepository.GetByIdAsync(id);
        if (userPoint == null || userPoint.IsDeleted)
            return null;

        return MapToUserPointDto(userPoint);
    }

    public async Task<Guid> CreateAsync(UserPointCreateDto dto)
    {
        // Get current balance
        var currentBalance = await _userPointRepository.GetCustomerCurrentBalanceAsync(dto.CustomerId);
        var newBalance = currentBalance + dto.Point;

        // Check if balance would go negative
        if (newBalance < 0)
            throw new InvalidOperationException("Puan bakiyesi negatif olamaz. Mevcut bakiye: " + currentBalance);

        var userPoint = new UserPoint
        {
            Id = Guid.CreateVersion7(),
            CustomerId = dto.CustomerId,
            Point = dto.Point,
            Balance = newBalance,
            Description = dto.Description,
            Status = PointStatus.Earned, // Direkt eklenen puanlar kazanılmış durumda
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _userPointRepository.AddAsync(userPoint);
        await _userPointRepository.SaveChangesAsync();

        // Add to history - zorunlu!
        await _userPointRepository.AddToHistoryAsync(userPoint, ChangeType.Created, Guid.Empty); // TODO: Get actual employee ID
        await _userPointRepository.SaveChangesAsync();

        return userPoint.Id;
    }

    public async Task<Guid> AddPointsAsync(UserPointAddDto dto)
    {
        var createDto = new UserPointCreateDto
        {
            CustomerId = dto.CustomerId,
            Point = dto.Point, // Positive value
            Description = dto.Description
        };

        return await CreateAsync(createDto);
    }

    public async Task<Guid> SubtractPointsAsync(UserPointSubtractDto dto)
    {
        // Check if customer has sufficient balance
        if (!await CanSubtractPointsAsync(dto.CustomerId, dto.Point))
        {
            var currentBalance = await GetCustomerCurrentBalanceAsync(dto.CustomerId);
            throw new InvalidOperationException($"Yetersiz puan bakiyesi. Mevcut bakiye: {currentBalance}, İstenen: {dto.Point}");
        }

        var createDto = new UserPointCreateDto
        {
            CustomerId = dto.CustomerId,
            Point = -dto.Point, // Negative value
            Description = dto.Description,
            Status = PointStatus.Spent
        };

        return await CreateAsync(createDto);
    }

    public async Task<UserPointBalanceDto?> GetCustomerBalanceAsync(Guid customerId)
    {
        var customer = await _userPointRepository.Query()
            .Where(up => up.CustomerId == customerId && !up.IsDeleted)
            .Select(up => up.Customer)
            .FirstOrDefaultAsync();

        if (customer == null)
            return null;

        var currentBalance = await _userPointRepository.GetCustomerCurrentBalanceAsync(customerId);
        var totalEarned = await _userPointRepository.GetCustomerTotalEarnedAsync(customerId);
        var totalSpent = await _userPointRepository.GetCustomerTotalSpentAsync(customerId);
        var transactionCount = await _userPointRepository.GetCustomerTransactionCountAsync(customerId);
        var lastTransaction = await _userPointRepository.GetLastPointTransactionAsync(customerId);

        return new UserPointBalanceDto
        {
            CustomerId = customerId,
            CustomerName = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "Bilinmeyen Müşteri",
            CurrentBalance = currentBalance,
            TotalEarned = totalEarned,
            TotalSpent = totalSpent,
            TransactionCount = transactionCount,
            LastActivity = lastTransaction?.CreatedAt ?? DateTime.MinValue
        };
    }

    public async Task<decimal> GetCustomerCurrentBalanceAsync(Guid customerId)
    {
        return await _userPointRepository.GetCustomerCurrentBalanceAsync(customerId);
    }

    public async Task<List<UserPointListDto>> GetCustomerPointsAsync(Guid customerId, int? page = null, int? pageSize = null)
    {
        List<UserPoint> userPoints;
        
        if (page.HasValue && pageSize.HasValue)
        {
            userPoints = await _userPointRepository.GetCustomerPointsPagedAsync(customerId, page.Value, pageSize.Value);
        }
        else
        {
            userPoints = await _userPointRepository.GetCustomerPointsAsync(customerId);
        }

        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    public async Task<UserPointSummaryDto?> GetCustomerSummaryAsync(Guid customerId)
    {
        var currentBalance = await _userPointRepository.GetCustomerCurrentBalanceAsync(customerId);
        var recentTransactions = await GetCustomerRecentPointsAsync(customerId, 5);

        return new UserPointSummaryDto
        {
            CustomerId = customerId,
            CurrentBalance = currentBalance,
            RecentTransactions = recentTransactions
        };
    }

    public async Task<List<UserPointListDto>> GetCustomerRecentPointsAsync(Guid customerId, int count = 10)
    {
        var userPoints = await _userPointRepository.GetCustomerRecentPointsAsync(customerId, count);
        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    public async Task<List<UserPointListDto>> GetPointHistoryAsync(Guid customerId, DateTime? startDate = null, DateTime? endDate = null)
    {
        List<UserPoint> userPoints;

        if (startDate.HasValue && endDate.HasValue)
        {
            userPoints = await _userPointRepository.GetCustomerPointsByDateRangeAsync(customerId, startDate.Value, endDate.Value);
        }
        else
        {
            userPoints = await _userPointRepository.GetCustomerPointsAsync(customerId);
        }

        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    public async Task<decimal> GetCustomerTotalEarnedAsync(Guid customerId)
    {
        return await _userPointRepository.GetCustomerTotalEarnedAsync(customerId);
    }

    public async Task<decimal> GetCustomerTotalSpentAsync(Guid customerId)
    {
        return await _userPointRepository.GetCustomerTotalSpentAsync(customerId);
    }

    public async Task<int> GetCustomerTransactionCountAsync(Guid customerId)
    {
        return await _userPointRepository.GetCustomerTransactionCountAsync(customerId);
    }

    public async Task<List<UserPointListDto>> SearchAsync(UserPointSearchDto searchDto)
    {
        var query = _userPointRepository.Query().Include(up => up.Customer).Include(up => up.Order).Where(up => !up.IsDeleted);

        // Apply filters
        if (searchDto.CustomerId.HasValue)
            query = query.Where(up => up.CustomerId == searchDto.CustomerId.Value);

        if (!string.IsNullOrEmpty(searchDto.CustomerName))
        {
            // Note: For encrypted fields, we need to decrypt and search, which is not efficient
            // In production, consider using searchable encryption or separate search index
            var allPoints = await query.ToListAsync();
            var filteredPoints = allPoints.Where(up =>
                _encryptionService.DecryptIfNotEmpty(up.Customer.NameSurname)?.Contains(searchDto.CustomerName, StringComparison.OrdinalIgnoreCase) == true
            ).ToList();

            return filteredPoints.Select(MapToUserPointListDto).ToList();
        }

        if (searchDto.MinPoint.HasValue)
            query = query.Where(up => up.Point >= searchDto.MinPoint.Value);

        if (searchDto.MaxPoint.HasValue)
            query = query.Where(up => up.Point <= searchDto.MaxPoint.Value);

        if (searchDto.StartDate.HasValue)
            query = query.Where(up => up.CreatedAt >= searchDto.StartDate.Value);

        if (searchDto.EndDate.HasValue)
            query = query.Where(up => up.CreatedAt <= searchDto.EndDate.Value);

        if (searchDto.IsPositive.HasValue)
            query = query.Where(up => searchDto.IsPositive.Value ? up.Point > 0 : up.Point < 0);

        // Apply sorting
        query = searchDto.SortDirection?.ToLower() == "asc" 
            ? query.OrderBy(up => up.CreatedAt)
            : query.OrderByDescending(up => up.CreatedAt);

        // Apply pagination
        if (searchDto.Page.HasValue && searchDto.PageSize.HasValue)
        {
            query = query.Skip((searchDto.Page.Value - 1) * searchDto.PageSize.Value)
                         .Take(searchDto.PageSize.Value);
        }

        var userPoints = await query.ToListAsync();
        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    public async Task<List<UserPointListDto>> GetPositivePointsAsync(Guid customerId)
    {
        var userPoints = await _userPointRepository.GetPositivePointsAsync(customerId);
        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    public async Task<List<UserPointListDto>> GetNegativePointsAsync(Guid customerId)
    {
        var userPoints = await _userPointRepository.GetNegativePointsAsync(customerId);
        return userPoints.Select(MapToUserPointListDto).ToList();
    }

    private UserPointDto MapToUserPointDto(UserPoint userPoint)
    {
        return new UserPointDto
        {
            Id = userPoint.Id,
            CustomerId = userPoint.CustomerId,
            CustomerName = _encryptionService.DecryptIfNotEmpty(userPoint.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
            Point = userPoint.Point,
            Balance = userPoint.Balance,
            Description = userPoint.Description,
            Status = userPoint.Status,
            OrderId = userPoint.OrderId,
            OrderNumber = userPoint.Order?.OrderNumber,
            CreatedAt = userPoint.CreatedAt,
            UpdatedAt = userPoint.UpdatedAt
        };
    }

    private UserPointListDto MapToUserPointListDto(UserPoint userPoint)
    {
        return new UserPointListDto
        {
            Id = userPoint.Id,
            CustomerId = userPoint.CustomerId,
            CustomerName = _encryptionService.DecryptIfNotEmpty(userPoint.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
            Point = userPoint.Point,
            Balance = userPoint.Balance,
            Description = userPoint.Description,
            PointType = userPoint.Point > 0 ? "Eklendi" : "Kullanıldı",
            Status = userPoint.Status,
            OrderId = userPoint.OrderId,
            OrderNumber = userPoint.Order?.OrderNumber,
            CreatedAt = userPoint.CreatedAt
        };
    }

    public async Task<UserPointAnalyticsDto> GetAnalyticsAsync()
    {
        var totalPointsInSystem = await _userPointRepository.GetTotalPointsInSystemAsync();
        var totalPointsEarned = await _userPointRepository.GetTotalPointsEarnedAsync();
        var totalPointsSpent = await _userPointRepository.GetTotalPointsSpentAsync();
        var activeCustomersWithPoints = await _userPointRepository.GetActiveCustomersWithPointsAsync();
        var averagePointBalance = await _userPointRepository.GetAveragePointBalanceAsync();

        var topCustomers = await GetTopCustomersAsync(10);
        var pointsByDate = await GetPointsByDateAsync(30);

        return new UserPointAnalyticsDto
        {
            TotalPointsInSystem = totalPointsInSystem,
            TotalPointsEarned = totalPointsEarned,
            TotalPointsSpent = totalPointsSpent,
            ActiveCustomersWithPoints = activeCustomersWithPoints,
            AveragePointBalance = averagePointBalance,
            TopCustomers = topCustomers,
            PointsByDate = pointsByDate
        };
    }

    public async Task<List<UserPointTopCustomersDto>> GetTopCustomersAsync(int count = 10)
    {
        var topCustomers = await _userPointRepository.GetTopCustomersByBalanceAsync(count);
        var result = new List<UserPointTopCustomersDto>();

        foreach (var userPoint in topCustomers)
        {
            var totalEarned = await _userPointRepository.GetCustomerTotalEarnedAsync(userPoint.CustomerId);
            var transactionCount = await _userPointRepository.GetCustomerTransactionCountAsync(userPoint.CustomerId);

            result.Add(new UserPointTopCustomersDto
            {
                CustomerId = userPoint.CustomerId,
                CustomerName = _encryptionService.DecryptIfNotEmpty(userPoint.Customer?.NameSurname) ?? "Bilinmeyen Müşteri",
                CurrentBalance = userPoint.Balance,
                TotalEarned = totalEarned,
                TransactionCount = transactionCount
            });
        }

        return result;
    }

    public async Task<List<UserPointByDateDto>> GetPointsByDateAsync(int days = 30)
    {
        var startDate = DateTime.UtcNow.AddDays(-days).Date;
        var endDate = DateTime.UtcNow.Date.AddDays(1);

        var pointsByDate = await _userPointRepository.Query()
            .Where(up => !up.IsDeleted && up.CreatedAt >= startDate && up.CreatedAt < endDate)
            .GroupBy(up => up.CreatedAt.Date)
            .Select(g => new UserPointByDateDto
            {
                Date = g.Key,
                PointsEarned = g.Where(up => up.Point > 0).Sum(up => up.Point),
                PointsSpent = g.Where(up => up.Point < 0).Sum(up => Math.Abs(up.Point)),
                NetPoints = g.Sum(up => up.Point),
                TransactionCount = g.Count()
            })
            .OrderBy(p => p.Date)
            .ToListAsync();

        return pointsByDate;
    }

    public async Task<bool> CanSubtractPointsAsync(Guid customerId, decimal points)
    {
        return await _userPointRepository.HasSufficientBalanceAsync(customerId, points);
    }

    public async Task<decimal> GetTotalPointsInSystemAsync()
    {
        return await _userPointRepository.GetTotalPointsInSystemAsync();
    }

    public async Task<int> GetActiveCustomersWithPointsAsync()
    {
        return await _userPointRepository.GetActiveCustomersWithPointsAsync();
    }

    public async Task<decimal> GetAveragePointBalanceAsync()
    {
        return await _userPointRepository.GetAveragePointBalanceAsync();
    }

    /// <summary>
    /// Sipariş sonrası beklemede puan ekler (bakiyeyi artırmaz) - OrderId ile
    /// </summary>
    public async Task<Guid> AddPendingPointsAsync(Guid customerId, decimal points, string description, Guid? orderId = null)
    {
        // Mevcut bakiye çekilir
        var currentBalance = await _userPointRepository.GetCustomerCurrentBalanceAsync(customerId);
        var userPoint = new UserPoint
        {
            Id = Guid.CreateVersion7(),
            CustomerId = customerId,
            Point = points,
            Balance = currentBalance, // Beklemede olan puanlar bakiyeyi etkilemez
            Description = description,
            Status = PointStatus.Pending,
            OrderId = orderId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _userPointRepository.AddAsync(userPoint);
        await _userPointRepository.SaveChangesAsync();

        // Add to history
        await _userPointRepository.AddToHistoryAsync(userPoint, ChangeType.Created, Guid.Empty);
        await _userPointRepository.SaveChangesAsync();

        return userPoint.Id;
    }

    /// <summary>
    /// Beklemede olan puanları kazanıldı durumuna geçirir ve bakiyeyi günceller
    /// </summary>
    public async Task<bool> EarnPendingPointsAsync(Guid userPointId)
    {
        var userPoint = await _userPointRepository.GetByIdAsync(userPointId);
        if (userPoint == null || userPoint.Status != PointStatus.Pending)
            return false;

        // Mevcut bakiyeyi al
        var currentBalance = await _userPointRepository.GetCustomerCurrentBalanceAsync(userPoint.CustomerId);
        var newBalance = currentBalance + userPoint.Point;

        // Puan durumunu güncelle ve bakiyeyi ayarla
        userPoint.Status = PointStatus.Earned;
        userPoint.Balance = newBalance;
        userPoint.UpdatedAt = DateTime.UtcNow;

        _userPointRepository.Update(userPoint);
        await _userPointRepository.SaveChangesAsync();

        // Add to history
        await _userPointRepository.AddToHistoryAsync(userPoint, ChangeType.Updated, Guid.Empty);
        await _userPointRepository.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// Beklemede olan puanları iptal eder
    /// </summary>
    public async Task<bool> CancelPendingPointsAsync(Guid userPointId)
    {
        var userPoint = await _userPointRepository.GetByIdAsync(userPointId);
        if (userPoint == null || userPoint.Status != PointStatus.Pending)
            return false;

        userPoint.Status = PointStatus.Cancelled;
        userPoint.UpdatedAt = DateTime.UtcNow;

        _userPointRepository.Update(userPoint);
        await _userPointRepository.SaveChangesAsync();

        // Add to history
        await _userPointRepository.AddToHistoryAsync(userPoint, ChangeType.Updated, Guid.Empty);
        await _userPointRepository.SaveChangesAsync();

        return true;
    }
}
