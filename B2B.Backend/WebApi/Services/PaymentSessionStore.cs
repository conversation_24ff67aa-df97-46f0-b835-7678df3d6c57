using System.Collections.Concurrent;

namespace WebApi.Services;

public interface IPaymentSessionStore
{
    void Save(string token, Guid customerId, Guid cartId, AddressSnapshot? shippingAddress = null, int usedPoints = 0);
    bool TryGet(string token, out PaymentSession session);
    bool Remove(string token);
}

public sealed class AddressSnapshot
{
    public Guid? AddressId { get; init; }
    public string? Name { get; init; }
    public string? Line1 { get; init; }
    public string? Line2 { get; init; }
    public string? City { get; init; }
    public string? District { get; init; }
    public string? Country { get; init; }
    public string? PostalCode { get; init; }
}

public sealed class PaymentSession
{
    public string Token { get; init; } = string.Empty;
    public Guid CustomerId { get; init; }
    public Guid CartId { get; init; }
    public int UsedPoints { get; init; }
    public AddressSnapshot? ShippingAddress { get; init; }
    public DateTime CreatedAtUtc { get; init; } = DateTime.UtcNow;
}

public sealed class InMemoryPaymentSessionStore : IPaymentSessionStore
{
    private readonly ConcurrentDictionary<string, PaymentSession> _store = new();
    private static readonly TimeSpan DefaultTtl = TimeSpan.FromMinutes(15);

    public void Save(string token, Guid customerId, Guid cartId, AddressSnapshot? shippingAddress = null, int usedPoints = 0)
    {
        if (string.IsNullOrWhiteSpace(token)) return;
        var session = new PaymentSession { Token = token, CustomerId = customerId, CartId = cartId, ShippingAddress = shippingAddress, UsedPoints = usedPoints };
        _store[token] = session;
    }

    public bool TryGet(string token, out PaymentSession session)
    {
        session = default!;
        if (string.IsNullOrWhiteSpace(token)) return false;
        if (_store.TryGetValue(token, out var s))
        {
            // simple TTL eviction
            if (DateTime.UtcNow - s.CreatedAtUtc > DefaultTtl)
            {
                _store.TryRemove(token, out _);
                return false;
            }
            session = s;
            return true;
        }
        return false;
    }

    public bool Remove(string token)
    {
        if (string.IsNullOrWhiteSpace(token)) return false;
        return _store.TryRemove(token, out _);
    }
}

