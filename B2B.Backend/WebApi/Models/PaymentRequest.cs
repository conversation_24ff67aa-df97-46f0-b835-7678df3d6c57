using System.ComponentModel.DataAnnotations;

namespace WebApi.Models;

public class PaymentRequest
{
    public ShippingAddressDto? ShippingAddress { get; set; }
    public BillingAddressDto? BillingAddress { get; set; }

    [Required]
    public List<CartProductDto> CartProducts { get; set; } = new();

    [Required]
    public ShippingInfoDto ShippingInfo { get; set; } = new();


    public string? BasketId { get; set; }

    [Required]
    public decimal TotalPrice { get; set; }

    public Guid? CustomerId { get; set; }
    public int UsedPoints { get; set; }
}

public class ShippingAddressDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Line1 { get; set; } = string.Empty;
    public string Line2 { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string District { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public int AddressType { get; set; }
    public bool IsDefault { get; set; }
}

public class BillingAddressDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Line1 { get; set; } = string.Empty;
    public string Line2 { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string District { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public int AddressType { get; set; }
    public bool IsDefault { get; set; }
}


public class ShippingInfoDto
{
    [Required]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    public string LastName { get; set; } = string.Empty;

    [Required]
    public string Address { get; set; } = string.Empty;

    [Required]
    public string City { get; set; } = string.Empty;

    public string ZipCode { get; set; } = string.Empty;

    [Required]
    public string Phone { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    public string? IdentityNumber { get; set; }
}
public class CartProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int Quantity { get; set; }
    public string Image { get; set; } = string.Empty;
    public decimal? OldPrice { get; set; }
}

public class PaymentInitializationResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PaymentPageUrl { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public Guid? OrderId { get; set; }
    public object? Data { get; set; }
}
