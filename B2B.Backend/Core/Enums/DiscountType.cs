namespace Core.Enums;

public enum DiscountType{
    Fixed,
    Percentage
}

public enum CampaignType
{
    /// <summary>
    /// Sepet İndirimi - Sepet toplamında indirim (Yüzde, Sabit Tutar, Sepette Ürün <PERSON>ndi<PERSON>)
    /// </summary>
    CartDiscount = 1,

    /// <summary>
    /// Ürün İndirimi - Ürünlere indirimli fiyat veya X Alana Y Bedava
    /// </summary>
    ProductDiscount = 2,

    /// <summary>
    /// Kargo İndirimi - X tutar üzeri kargo bedava
    /// </summary>
    ShippingDiscount = 3
}

public enum DiscountCalculationType
{
    /// <summary>
    /// Sepet Yüzde İndirim
    /// </summary>
    CartPercentage = 1,

    /// <summary>
    /// Sepet Sabit Tutar İndirim
    /// </summary>
    CartFixedAmount = 2,

    /// <summary>
    /// Ürün Yüzde İndirim
    /// </summary>
    ProductPercentage = 3,

    /// <summary>
    /// X Alana Y Bedava
    /// </summary>
    BuyXGetYFree = 4,

    /// <summary>
    /// He<PERSON>ye <PERSON>
    /// </summary>
    FreeProduct = 5,

    /// <summary>
    /// Kargo Bedava
    /// </summary>
    FreeShipping = 6
}

public enum CampaignRuleType
{
    /// <summary>
    /// Sepet minimum tutar kontrolü
    /// </summary>
    CartMinimumAmount = 1,

    /// <summary>
    /// Ürün/Kategori seçimi
    /// </summary>
    ProductSelection = 2,

    /// <summary>
    /// X Alana Y Bedava kuralı
    /// </summary>
    BuyXGetYFree = 3,

    /// <summary>
    /// Kargo bedava minimum tutar
    /// </summary>
    ShippingMinimumAmount = 4
}

public enum CampaignTargetType
{
    /// <summary>
    /// Belirli ürünler
    /// </summary>
    Product = 1,

    /// <summary>
    /// Belirli kategoriler
    /// </summary>
    Category = 2,

    /// <summary>
    /// Tüm sepet
    /// </summary>
    Cart = 3,

    /// <summary>
    /// Kargo
    /// </summary>
    Shipping = 4
}

public enum CampaignStatus
{
    /// <summary>
    /// Taslak - Henüz aktif değil
    /// </summary>
    Draft = 0,

    /// <summary>
    /// Aktif - Çalışıyor
    /// </summary>
    Active = 1,

    /// <summary>
    /// Pasif - Durdurulmuş
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// Süresi dolmuş
    /// </summary>
    Expired = 3,

    /// <summary>
    /// Kullanım limiti dolmuş
    /// </summary>
    LimitReached = 4
}

public enum PointStatus
{
    /// <summary>
    /// Beklemede - Henüz geçerlilik süresi dolmamış
    /// </summary>
    Pending = 0,

    /// <summary>
    /// Kazanıldı - Geçerlilik süresi dolmuş ve bakiyeye eklenmiş
    /// </summary>
    Earned = 1,

    /// <summary>
    /// İptal Edildi - Sipariş iptal edildiği için puan iptal edildi
    /// </summary>
    Cancelled = 2,

    /// <summary>
    /// Kullanıldı - Puan kullanıldı
    /// </summary>
    Spent = 3
}

public enum CouponType
{
    /// <summary>
    /// Müşteri Özel - Belirli bir müşteriye özel kupon
    /// </summary>
    CustomerSpecific = 0,

    /// <summary>
    /// Genel - Tüm müşterilerin kullanabileceği kupon
    /// </summary>
    General = 1
}

