using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class CouponController : ControllerBase
{
    private readonly ICouponService _couponService;

    public CouponController(ICouponService couponService)
    {
        _couponService = couponService;
    }

    [HttpGet]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponListDto>>>> GetList([FromQuery] int? page, [FromQuery] int? pageSize)
    {
        try
        {
            var coupons = await _couponService.GetListAsync(page, pageSize);
            return Ok(ApiResponse<List<CouponListDto>>.SuccessResponse(coupons, "<PERSON>ponlar başarıyla listelendi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponListDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("{id}")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<CouponDto>>> GetById(Guid id)
    {
        try
        {
            var coupon = await _couponService.GetByIdAsync(id);
            if (coupon == null)
                return NotFound(ApiResponse<CouponDto>.NotFoundResponse("Kupon bulunamadı"));

            return Ok(ApiResponse<CouponDto>.SuccessResponse(coupon, "Kupon başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CouponDto>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("code/{couponCode}")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<CouponDto>> GetByCouponCode(string couponCode)
    {
        try
        {
            var coupon = await _couponService.GetByCouponCodeAsync(couponCode);
            if (coupon == null)
                return NotFound(new { message = "Kupon bulunamadı" });

            return Ok(coupon);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost]
    [RequirePermission("coupon", "create")]
    public async Task<ActionResult<ApiResponse<Guid>>> Create([FromBody] CouponCreateRequest request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ApiResponse<Guid>.BadRequestResponse("Geçersiz veri gönderildi."));

        try
        {
            var couponId = await _couponService.CreateAsync(request.Dto);
            return Ok(ApiResponse<Guid>.SuccessResponse(couponId, "Kupon başarıyla oluşturuldu."));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ApiResponse<Guid>.ErrorResponse(ex.Message));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<Guid>.ErrorResponse(ex.Message));
        }
    }

    [HttpPut("{id}")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> Update(Guid id, [FromBody] CouponUpdateRequest request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (id != request.Dto.Id)
            return BadRequest(new { message = "ID uyuşmazlığı" });

        try
        {
            await _couponService.UpdateAsync(request.Dto);
            return Ok(new { message = "Kupon başarıyla güncellendi." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [RequirePermission("coupon", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            await _couponService.DeleteAsync(id);
            return Ok(new { message = "Kupon başarıyla silindi." });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("validate")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<CouponValidationDto>> ValidateCoupon([FromBody] CouponUsageDto dto)
    {
        try
        {
            var validation = await _couponService.ValidateCouponAsync(dto.CouponCode, dto.CustomerId);
            return Ok(validation);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("use")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> UseCoupon([FromBody] CouponUsageDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var success = await _couponService.UseCouponAsync(dto);
            if (!success)
                return BadRequest(new { message = "Kupon kullanılamadı" });

            return Ok(new { message = "Kupon başarıyla kullanıldı." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }



    [HttpGet("customer/{customerId}/active")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetCustomerActiveCoupons(Guid customerId)
    {
        try
        {
            var coupons = await _couponService.GetCustomerActiveCouponsAsync(customerId);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("customer/{customerId}/used")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetCustomerUsedCoupons(Guid customerId)
    {
        try
        {
            var coupons = await _couponService.GetCustomerUsedCouponsAsync(customerId);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("customer/{customerId}/expired")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetCustomerExpiredCoupons(Guid customerId)
    {
        try
        {
            var coupons = await _couponService.GetCustomerExpiredCouponsAsync(customerId);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/extend-expiration")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> ExtendExpiration(Guid id, [FromBody] DateTime newExpirationDate)
    {
        try
        {
            var success = await _couponService.ExtendExpirationAsync(id, newExpirationDate);
            if (!success)
                return NotFound(new { message = "Kupon bulunamadı" });

            return Ok(new { message = "Kupon süresi başarıyla uzatıldı." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/reset-usage")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> ResetUsage(Guid id)
    {
        try
        {
            var success = await _couponService.ResetUsageAsync(id);
            if (!success)
                return NotFound(new { message = "Kupon bulunamadı" });

            return Ok(new { message = "Kupon kullanımı başarıyla sıfırlandı." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/deactivate")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> DeactivateCoupon(Guid id)
    {
        try
        {
            var success = await _couponService.DeactivateCouponAsync(id);
            if (!success)
                return NotFound(new { message = "Kupon bulunamadı" });

            return Ok(new { message = "Kupon başarıyla deaktif edildi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/activate")]
    [RequirePermission("coupon", "update")]
    public async Task<ActionResult> ActivateCoupon(Guid id)
    {
        try
        {
            var success = await _couponService.ActivateCouponAsync(id);
            if (!success)
                return NotFound(new { message = "Kupon bulunamadı" });

            return Ok(new { message = "Kupon başarıyla aktif edildi." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("bulk-create")]
    [RequirePermission("coupon", "create")]
    public async Task<ActionResult> CreateBulkCoupons([FromBody] BulkCouponCreateDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var couponIds = await _couponService.CreateBulkCouponsAsync(dto);
            return Ok(new { 
                message = $"{couponIds.Count} adet kupon başarıyla oluşturuldu.", 
                couponIds = couponIds 
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("search")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> Search([FromBody] CouponSearchDto searchDto)
    {
        try
        {
            var coupons = await _couponService.SearchAsync(searchDto);
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("active")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetActiveCoupons()
    {
        try
        {
            var coupons = await _couponService.GetActiveCouponsAsync();
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("expired")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetExpiredCoupons()
    {
        try
        {
            var coupons = await _couponService.GetExpiredCouponsAsync();
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("used")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponListDto>>> GetUsedCoupons()
    {
        try
        {
            var coupons = await _couponService.GetUsedCouponsAsync();
            return Ok(coupons);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("analytics")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<CouponAnalyticsDto>> GetAnalytics()
    {
        try
        {
            var analytics = await _couponService.GetAnalyticsAsync();
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("top-customers")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<List<CouponTopCustomersDto>>> GetTopCustomers([FromQuery] int count = 10)
    {
        try
        {
            var topCustomers = await _couponService.GetTopCustomersAsync(count);
            return Ok(topCustomers);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("statistics")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult> GetStatistics()
    {
        try
        {
            var totalCoupons = await _couponService.GetTotalCouponsAsync();
            var activeCoupons = await _couponService.GetActiveCouponsCountAsync();
            var expiredCoupons = await _couponService.GetExpiredCouponsCountAsync();
            var totalDiscountGiven = await _couponService.GetTotalDiscountGivenAsync();

            return Ok(new { 
                totalCoupons = totalCoupons,
                activeCoupons = activeCoupons,
                expiredCoupons = expiredCoupons,
                totalDiscountGiven = totalDiscountGiven
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    // Yeni kupon sistemi endpoint'leri

    [HttpGet("general")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponListDto>>>> GetGeneralCoupons([FromQuery] int? page, [FromQuery] int? pageSize)
    {
        try
        {
            var coupons = await _couponService.GetGeneralCouponsAsync(page, pageSize);
            return Ok(ApiResponse<List<CouponListDto>>.SuccessResponse(coupons, "Genel kuponlar başarıyla listelendi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponListDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("customer-specific")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponListDto>>>> GetCustomerSpecificCoupons([FromQuery] int? page, [FromQuery] int? pageSize)
    {
        try
        {
            var coupons = await _couponService.GetCustomerSpecificCouponsAsync(page, pageSize);
            return Ok(ApiResponse<List<CouponListDto>>.SuccessResponse(coupons, "Müşteri özel kuponları başarıyla listelendi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponListDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("{couponId}/usage-history")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponUsageDto>>>> GetCouponUsageHistory(Guid couponId)
    {
        try
        {
            var usageHistory = await _couponService.GetCouponUsageHistoryAsync(couponId);
            return Ok(ApiResponse<List<CouponUsageDto>>.SuccessResponse(usageHistory, "Kupon kullanım geçmişi başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponUsageDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("customer/{customerId}/usage-history")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponUsageDto>>>> GetCustomerCouponUsageHistory(Guid customerId)
    {
        try
        {
            var usageHistory = await _couponService.GetCustomerCouponUsageHistoryAsync(customerId);
            return Ok(ApiResponse<List<CouponUsageDto>>.SuccessResponse(usageHistory, "Müşteri kupon kullanım geçmişi başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponUsageDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpPost("validate-for-customer")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<CouponValidationDto>>> ValidateCouponForCustomer([FromBody] ValidateCouponForCustomerDto dto)
    {
        try
        {
            var validation = await _couponService.ValidateCouponAsync(dto.CouponCode, dto.CustomerId);
            return Ok(ApiResponse<CouponValidationDto>.SuccessResponse(validation, "Kupon doğrulaması tamamlandı."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CouponValidationDto>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("customer/{customerId}")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<List<CouponListDto>>>> GetCustomerCoupons(Guid customerId)
    {
        try
        {
            var coupons = await _couponService.GetCustomerCouponsAsync(customerId);
            return Ok(ApiResponse<List<CouponListDto>>.SuccessResponse(coupons, "Müşteri kuponları başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<List<CouponListDto>>.ErrorResponse(ex.Message));
        }
    }

    [HttpGet("customer/{customerId}/summary")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<CouponSummaryDto>>> GetCustomerCouponSummary(Guid customerId)
    {
        try
        {
            var summary = await _couponService.GetCustomerSummaryAsync(customerId);
            if (summary == null)
                return NotFound(ApiResponse<CouponSummaryDto>.NotFoundResponse("Müşteri kupon özeti bulunamadı"));

            return Ok(ApiResponse<CouponSummaryDto>.SuccessResponse(summary, "Müşteri kupon özeti başarıyla getirildi."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CouponSummaryDto>.ErrorResponse(ex.Message));
        }
    }

    [HttpPost("validate-for-order")]
    [RequirePermission("coupon", "read")]
    public async Task<ActionResult<ApiResponse<CouponValidationDto>>> ValidateCouponForOrder([FromBody] ValidateCouponForOrderDto dto)
    {
        try
        {
            var validation = await _couponService.ValidateCouponForOrderAsync(dto.CouponCode, dto.CustomerId, dto.OrderAmount);
            return Ok(ApiResponse<CouponValidationDto>.SuccessResponse(validation, "Kupon doğrulaması tamamlandı."));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CouponValidationDto>.ErrorResponse(ex.Message));
        }
    }
}
