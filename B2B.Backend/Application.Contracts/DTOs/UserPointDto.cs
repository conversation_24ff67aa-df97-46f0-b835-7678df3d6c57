using System.ComponentModel.DataAnnotations;
using Core.Enums;

namespace Application.Contracts.DTOs;

public class UserPointDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public decimal Point { get; set; }
    public decimal Balance { get; set; }
    public string Description { get; set; } = null!;
    public PointStatus Status { get; set; }
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserPointListDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public decimal Point { get; set; }
    public decimal Balance { get; set; }
    public string Description { get; set; } = null!;
    public string PointType { get; set; } = null!; // "Eklendi" / "Kullanıldı"
    public PointStatus Status { get; set; }
    public Guid? OrderId { get; set; }
    public string? OrderNumber { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class UserPointCreateDto
{
    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [Range(-999999, 999999, ErrorMessage = "Puan -999999 ile 999999 arasında olmalıdır")]
    public decimal Point { get; set; }

    [Required]
    [StringLength(500, MinimumLength = 3)]
    public string Description { get; set; } = null!;
    public PointStatus Status { get; set; } = PointStatus.Earned;
}

public class UserPointAddDto
{
    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "Eklenecek puan 0'dan büyük olmalıdır")]
    public decimal Point { get; set; }

    [Required]
    [StringLength(500, MinimumLength = 3)]
    public string Description { get; set; } = null!;
}

public class UserPointSubtractDto
{
    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "Düşülecek puan 0'dan büyük olmalıdır")]
    public decimal Point { get; set; }

    [Required]
    [StringLength(500, MinimumLength = 3)]
    public string Description { get; set; } = null!;
}

public class UserPointBalanceDto
{
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public decimal CurrentBalance { get; set; }
    public decimal TotalEarned { get; set; }
    public decimal TotalSpent { get; set; }
    public int TransactionCount { get; set; }
    public DateTime LastActivity { get; set; }
}

public class UserPointSummaryDto
{
    public Guid CustomerId { get; set; }
    public decimal CurrentBalance { get; set; }
    public List<UserPointListDto> RecentTransactions { get; set; } = [];
}

public class UserPointAnalyticsDto
{
    public decimal TotalPointsInSystem { get; set; }
    public decimal TotalPointsEarned { get; set; }
    public decimal TotalPointsSpent { get; set; }
    public int ActiveCustomersWithPoints { get; set; }
    public decimal AveragePointBalance { get; set; }
    public List<UserPointTopCustomersDto> TopCustomers { get; set; } = [];
    public List<UserPointByDateDto> PointsByDate { get; set; } = [];
}

public class UserPointTopCustomersDto
{
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public decimal CurrentBalance { get; set; }
    public decimal TotalEarned { get; set; }
    public int TransactionCount { get; set; }
}

public class UserPointByDateDto
{
    public DateTime Date { get; set; }
    public decimal PointsEarned { get; set; }
    public decimal PointsSpent { get; set; }
    public decimal NetPoints { get; set; }
    public int TransactionCount { get; set; }
}

public class UserPointSearchDto
{
    public Guid? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public decimal? MinPoint { get; set; }
    public decimal? MaxPoint { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? IsPositive { get; set; } // true: eklenen puanlar, false: düşülen puanlar
    public int? Page { get; set; } = 1;
    public int? PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
}
