namespace Application.Contracts.DTOs;

/// <summary>
/// Basit varyant oluşturma için DTO
/// </summary>
public class ProductVariantCreateDto
{
    public string Name { get; set; } = null!;
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public int? StockQuantity { get; set; }
    public decimal? PointValue { get; set; }
    public bool IsActive { get; set; } = false;
    public List<ProductAttributeMappingCreateDto> AttributeMappings { get; set; } = new();
}

/// <summary>
/// Varyant attribute seçimi için DTO
/// </summary>
public class VariantAttributeSelection
{
    public Guid AttributeId { get; set; }
    public List<Guid> SelectedValueIds { get; set; } = new();
}

/// <summary>
/// Varyant kombinasyonu için DTO
/// </summary>
public class ProductVariantCombination
{
    public string Name { get; set; } = null!;
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public int? StockQuantity { get; set; }
    public decimal? PointValue { get; set; }
    public List<ProductAttributeMappingCreateDto> AttributeMappings { get; set; } = new();
}

/// <summary>
/// Ana ürün + varyantları oluşturmak için DTO
/// </summary>
public class ProductWithVariantsCreateDto
{
    // Ana ürün bilgileri
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public Guid? CategoryId { get; set; }
    public Guid? BrandId { get; set; }

    // Ürün özellikleri (IsListAttribute=true) - Ana ürüne atanacak
    public List<ProductAttributeMappingCreateDto> ProductAttributes { get; set; } = new();

    // Varyant özellikleri (IsVariantAttribute=true) - Kombinasyon oluşturacak
    public List<VariantAttributeSelection> VariantSelections { get; set; } = new();

    // Ortak bilgiler
    public decimal? BasePrice { get; set; }
    public List<ProductImageCreateDto>? Images { get; set; }
    public List<ProductFaqCreateDto>? Faqs { get; set; }
    public ProductSeoDto? Seo { get; set; }

    // Varyant kombinasyonları (frontend'ten hesaplanıp gönderilecek)
    public List<ProductVariantCombination> VariantCombinations { get; set; } = new();
}

/// <summary>
/// Varyant kombinasyon oluşturma response DTO
/// </summary>
public class ProductWithVariantsCreateResponse
{
    public Guid ParentProductId { get; set; }
    public List<Guid> VariantProductIds { get; set; } = new();
    public string Message { get; set; } = null!;
    public int TotalVariantsCreated { get; set; }
}

/// <summary>
/// Varyant kombinasyon önizleme için DTO
/// </summary>
public class VariantCombinationPreview
{
    public string CombinationName { get; set; } = null!;
    public List<AttributeValuePair> AttributeValues { get; set; } = new();
    public string? GeneratedSku { get; set; }
}

/// <summary>
/// Attribute-Value çifti için DTO
/// </summary>
public class AttributeValuePair
{
    public Guid AttributeId { get; set; }
    public string AttributeName { get; set; } = null!;
    public Guid ValueId { get; set; }
    public string ValueName { get; set; } = null!;
}
