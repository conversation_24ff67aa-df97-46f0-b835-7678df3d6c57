using System.ComponentModel.DataAnnotations;
using Core.Enums;

namespace Application.Contracts.DTOs;

public class CouponDto
{
    public Guid Id { get; set; }
    public string CouponCode { get; set; } = null!;
    public string? Name { get; set; }
    public string? Description { get; set; }
    public DiscountType DiscountType { get; set; }
    public string DiscountTypeText { get; set; } = null!;
    public decimal DiscountAmount { get; set; }
    public DateTime ExpirationDate { get; set; }
    public CouponType CouponType { get; set; }
    public string CouponTypeText { get; set; } = null!;
    public Guid? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public int? TotalUsageLimit { get; set; }
    public int UsageLimitPerCustomer { get; set; }
    public int TotalUsageCount { get; set; }
    public decimal? MinimumCartAmount { get; set; }
    public bool IsExpired { get; set; }
    public bool IsActive { get; set; }
    public int RemainingUsage { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CouponListDto
{
    public Guid Id { get; set; }
    public string CouponCode { get; set; } = null!;
    public string? Name { get; set; }
    public CouponType CouponType { get; set; }
    public string CouponTypeText { get; set; } = null!;
    public Guid? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public string DiscountTypeText { get; set; } = null!;
    public decimal DiscountAmount { get; set; }
    public DateTime ExpirationDate { get; set; }
    public string Status { get; set; } = null!; // "Aktif", "Kullanıldı", "Süresi Doldu"
    public int TotalUsageCount { get; set; }
    public int? TotalUsageLimit { get; set; }
    public int UsageLimitPerCustomer { get; set; }
    public decimal? MinimumCartAmount { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CouponCreateDto
{
    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string CouponCode { get; set; } = null!;

    [StringLength(200)]
    public string? Name { get; set; }

    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    public DiscountType DiscountType { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "İndirim miktarı 0'dan büyük olmalıdır")]
    public decimal DiscountAmount { get; set; }

    [Required]
    public DateTime ExpirationDate { get; set; }

    [Required]
    public CouponType CouponType { get; set; }

    // Müşteri özel kupon için gerekli (CouponType.CustomerSpecific ise)
    public Guid? CustomerId { get; set; }

    // Toplam kullanım limiti (null = sınırsız)
    public int? TotalUsageLimit { get; set; }

    // Müşteri başına kullanım limiti
    [Range(1, 999999, ErrorMessage = "Müşteri başına kullanım limiti 1'den büyük olmalıdır")]
    public int UsageLimitPerCustomer { get; set; } = 1;

    // Minimum sepet tutarı
    public decimal? MinimumCartAmount { get; set; }
}

public class CouponCreateRequest
{
    [Required]
    public CouponCreateDto Dto { get; set; } = null!;
}

public class CouponUpdateDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string CouponCode { get; set; } = null!;

    [StringLength(200)]
    public string? Name { get; set; }

    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    public DiscountType DiscountType { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "İndirim miktarı 0'dan büyük olmalıdır")]
    public decimal DiscountAmount { get; set; }

    [Required]
    public DateTime ExpirationDate { get; set; }

    [Required]
    public CouponType CouponType { get; set; }

    // Müşteri özel kupon için gerekli (CouponType.CustomerSpecific ise)
    public Guid? CustomerId { get; set; }

    // Toplam kullanım limiti (null = sınırsız)
    public int? TotalUsageLimit { get; set; }

    // Müşteri başına kullanım limiti
    [Range(1, 999999, ErrorMessage = "Müşteri başına kullanım limiti 1'den büyük olmalıdır")]
    public int UsageLimitPerCustomer { get; set; } = 1;

    // Minimum sepet tutarı
    public decimal? MinimumCartAmount { get; set; }

    public bool IsActive { get; set; } = true;
}

public class CouponUsageDto
{
    [Required]
    public Guid CouponId { get; set; }

    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [StringLength(50)]
    public string CouponCode { get; set; } = null!;

    // Sipariş ID'si (opsiyonel)
    public Guid? OrderId { get; set; }

    // Uygulanan indirim miktarı
    [Required]
    [Range(0.01, 999999, ErrorMessage = "İndirim miktarı 0'dan büyük olmalıdır")]
    public decimal DiscountApplied { get; set; }

    // Sipariş tutarı (kupon uygulanmadan önce)
    [Required]
    [Range(0.01, 999999, ErrorMessage = "Sipariş tutarı 0'dan büyük olmalıdır")]
    public decimal OrderAmount { get; set; }
}

public class CouponValidationDto
{
    public bool IsValid { get; set; }
    public string Message { get; set; } = null!;
    public CouponDto? Coupon { get; set; }
    public decimal DiscountAmount { get; set; }
    public DiscountType DiscountType { get; set; }
}

public class CouponAnalyticsDto
{
    public int TotalCoupons { get; set; }
    public int ActiveCoupons { get; set; }
    public int UsedCoupons { get; set; }
    public int ExpiredCoupons { get; set; }
    public decimal TotalDiscountGiven { get; set; }
    public decimal AverageDiscountAmount { get; set; }
    public List<CouponTopCustomersDto> TopCustomers { get; set; } = [];
    public List<CouponUsageByDateDto> UsageByDate { get; set; } = [];
}

public class CouponTopCustomersDto
{
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = null!;
    public int CouponCount { get; set; }
    public int UsedCouponCount { get; set; }
    public decimal TotalDiscountUsed { get; set; }
}

public class CouponUsageByDateDto
{
    public DateTime Date { get; set; }
    public int CouponsCreated { get; set; }
    public int CouponsUsed { get; set; }
    public decimal TotalDiscountGiven { get; set; }
}

public class CouponSearchDto
{
    public Guid? CustomerId { get; set; }
    public string? CustomerName { get; set; }
    public string? CouponCode { get; set; }
    public DiscountType? DiscountType { get; set; }
    public decimal? MinDiscountAmount { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? IsUsed { get; set; }
    public bool? IsExpired { get; set; }
    public bool? IsActive { get; set; }
    public int? Page { get; set; } = 1;
    public int? PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public string? SortDirection { get; set; } = "desc";
}

public class BulkCouponCreateDto
{
    [Required]
    public List<Guid> CustomerIds { get; set; } = [];

    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string CouponCodePrefix { get; set; } = null!;

    [Required]
    public DiscountType DiscountType { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "İndirim miktarı 0'dan büyük olmalıdır")]
    public decimal DiscountAmount { get; set; }

    [Required]
    public DateTime ExpirationDate { get; set; }

    [Range(1, 999999, ErrorMessage = "Kullanım limiti 1'den büyük olmalıdır")]
    public int UsageLimit { get; set; } = 1;

    public bool IsSingleUse { get; set; } = true;
}

public class CouponSummaryDto
{
    public Guid CustomerId { get; set; }
    public int TotalCoupons { get; set; }
    public int ActiveCoupons { get; set; }
    public int UsedCoupons { get; set; }
    public int ExpiredCoupons { get; set; }
    public List<CouponListDto> RecentCoupons { get; set; } = [];
}

public class ValidateCouponForCustomerDto
{
    [Required]
    [StringLength(50)]
    public string CouponCode { get; set; } = null!;

    [Required]
    public Guid CustomerId { get; set; }
}

public class ValidateCouponForOrderDto
{
    [Required]
    [StringLength(50)]
    public string CouponCode { get; set; } = null!;

    [Required]
    public Guid CustomerId { get; set; }

    [Required]
    [Range(0.01, 999999, ErrorMessage = "Sipariş tutarı 0'dan büyük olmalıdır")]
    public decimal OrderAmount { get; set; }
}
