using Core.Enums;

namespace Application.Contracts.DTOs;

public class ProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
    public ProductType ProductType { get; set; }
    public ProductDto? Parent { get; set; }
    public ProductCategoryDto? Category { get; set; }
    public ProductBrandDto? Brand { get; set; }
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public decimal? PointValue { get; set; }
    public int? StockQuantity { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public ICollection<ProductDto> Variants { get; set; } = new List<ProductDto>();
    public ICollection<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();
    public ICollection<ProductFaqDto> Faqs { get; set; } = new List<ProductFaqDto>();
    public ICollection<ProductReviewDto> Reviews { get; set; } = new List<ProductReviewDto>();
    public ICollection<ProductAttributeMappingDto> AttributeMappings { get; set; } = new List<ProductAttributeMappingDto>();
    public ProductSeoDto? Seo { get; set; }
}

public class ProductCreateDto
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public ProductType ProductType { get; set; } = ProductType.Simple;
    public Guid? CategoryId { get; set; }
    public Guid? BrandId { get; set; }
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public decimal? PointValue { get; set; }
    public int? StockQuantity { get; set; }

    // Related data
    public List<ProductAttributeMappingCreateDto>? AttributeMappings { get; set; }
    public List<ProductImageCreateDto>? Images { get; set; }
    public List<ProductFaqCreateDto>? Faqs { get; set; }
    public ProductSeoDto? Seo { get; set; }
    public List<ProductVariantCreateDto>? Variants { get; set; }
}

public class ProductUpdateDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public ProductType ProductType { get; set; }
    public Guid? CategoryId { get; set; }
    public Guid? BrandId { get; set; }
    public string? Sku { get; set; }
    public string? Barcode { get; set; }
    public decimal? Price { get; set; }
    public decimal? DiscountedPrice { get; set; }
    public decimal? PointValue { get; set; }
    public int? StockQuantity { get; set; }
    public bool IsActive { get; set; } = true;

    // Related data
    public List<ProductAttributeMappingCreateDto>? AttributeMappings { get; set; }
    public List<ProductFaqCreateDto>? Faqs { get; set; }
    public ProductSeoDto? Seo { get; set; }
    public List<ProductVariantCreateDto>? Variants { get; set; }
}

