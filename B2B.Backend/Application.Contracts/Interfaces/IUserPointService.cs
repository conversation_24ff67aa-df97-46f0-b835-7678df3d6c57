using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface IUserPointService
{
    // Basic CRUD operations
    Task<List<UserPointListDto>> GetListAsync(int? page = null, int? pageSize = null);
    Task<UserPointDto?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(UserPointCreateDto dto);

    // Point operations (temel işlemler)
    Task<Guid> AddPointsAsync(UserPointAddDto dto);
    Task<Guid> SubtractPointsAsync(UserPointSubtractDto dto);
    Task<UserPointBalanceDto?> GetCustomerBalanceAsync(Guid customerId);
    Task<decimal> GetCustomerCurrentBalanceAsync(Guid customerId);

    // Customer point operations
    Task<List<UserPointListDto>> GetCustomerPointsAsync(Guid customerId, int? page = null, int? pageSize = null);
    Task<UserPointSummaryDto?> GetCustomerSummaryAsync(Guid customerId);
    Task<List<UserPointListDto>> GetCustomerRecentPointsAsync(Guid customerId, int count = 10);

    // Point history and tracking
    Task<List<UserPointListDto>> GetPointHistoryAsync(Guid customerId, DateTime? startDate = null, DateTime? endDate = null);
    Task<decimal> GetCustomerTotalEarnedAsync(Guid customerId);
    Task<decimal> GetCustomerTotalSpentAsync(Guid customerId);
    Task<int> GetCustomerTransactionCountAsync(Guid customerId);

    // Search and filter operations
    Task<List<UserPointListDto>> SearchAsync(UserPointSearchDto searchDto);
    Task<List<UserPointListDto>> GetPositivePointsAsync(Guid customerId);
    Task<List<UserPointListDto>> GetNegativePointsAsync(Guid customerId);

    // Analytics and reporting
    Task<UserPointAnalyticsDto> GetAnalyticsAsync();
    Task<List<UserPointTopCustomersDto>> GetTopCustomersAsync(int count = 10);
    Task<List<UserPointByDateDto>> GetPointsByDateAsync(int days = 30);

    // Validation operations
    Task<bool> CanSubtractPointsAsync(Guid customerId, decimal points);

    // Statistics
    Task<decimal> GetTotalPointsInSystemAsync();
    Task<int> GetActiveCustomersWithPointsAsync();
    Task<decimal> GetAveragePointBalanceAsync();

    // Point status operations
    Task<Guid> AddPendingPointsAsync(Guid customerId, decimal points, string description, Guid? orderId);
    Task<bool> EarnPendingPointsAsync(Guid userPointId);
    Task<bool> CancelPendingPointsAsync(Guid userPointId);
}
